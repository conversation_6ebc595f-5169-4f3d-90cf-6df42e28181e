-- Idris data extracted from standardized California Housing dataset
-- First 100 samples from training set (same preprocessing as Python)

let standardizedInputs : Matrix SAMPLE_SIZE FEATURES = [
    [0.14460782, -0.17790253, -0.38568709, -0.15935610, 2.02730820, 4.24491765, -0.87173864, 0.84731976],
    [0.13797541, 0.53963009, -0.09281217, -0.15142968, 1.23203707, 1.09623285, -0.72458515, 0.64636642],
    [-0.56505969, -1.37379024, -0.21133403, -0.11089243, 0.03092229, 0.36692122, 0.87511571, -0.66987796],
    [0.27988786, 1.89496949, 0.01753845, -0.15373906, -0.22444000, -0.17401266, 1.00328166, -1.49881049],
    [-0.07720933, -0.17790253, -0.19182994, -0.00814009, -0.31381680, -1.38463945, -0.70085072, 0.54086592],
    [0.78305592, 1.09771102, 0.25064517, -0.06969386, -0.43511389, -0.61593115, 0.72796222, -1.24259498],
    [-0.98158535, 1.89496949, 0.15363146, 0.07886403, -0.74246065, -0.30876517, 1.01277543, -1.45861983],
    [1.52925430, -0.73598346, 0.39145288, -0.09694891, 0.23521212, 0.31115955, 0.65201203, -1.09187998],
    [-0.58385151, -0.49680592, -0.24397865, -0.06184738, -0.38768947, -0.51299854, 1.57765496, -0.77035463],
    [0.08960096, 1.01798517, 0.58903766, 0.34737698, -0.40775365, -0.19227209, 1.41626081, -1.66459700],
    [1.74464960, -1.29406439, 0.22764638, -0.20055143, -0.34664910, -0.21419399, -0.98566393, 0.90760576],
    [-1.60055734, -1.21433854, -0.92967644, -0.07934666, -0.74610868, -2.01518055, -0.70085072, 0.63129492],
    [-0.71907891, -0.17790253, -0.47812783, -0.14299911, 0.08564278, -0.00750120, -0.87648553, 0.77698609],
    [-0.07499853, 1.17743686, -0.03828410, -0.19685326, -0.63757971, 0.43775367, -0.77680091, 0.59612809],
    [0.91323006, 1.25716271, 0.25645044, -0.08438784, -0.53361078, -0.67966893, -0.78629468, 0.55091358],
    [-0.46867924, 1.17743686, -0.33524090, -0.13691610, -0.08307873, 0.86714676, -0.73407892, 0.71670009],
    [-1.12528750, -1.37379024, 4.40275571, 5.66376365, -1.05254343, -1.00687859, 2.38462573, -0.63471113],
    [0.85974970, -0.17790253, 0.20662493, -0.07795272, -0.21805594, 0.44004135, -0.85749798, 0.60115192],
    [-0.99337630, -1.21433854, -0.21534780, -0.13305985, -0.14418328, 0.38165629, -0.90971374, 1.21405961],
    [0.01906584, -0.09817669, 0.00347239, -0.16177137, 0.91009818, -0.39393115, 1.44474213, -0.86580747],
    [1.22295292, 0.38017840, 0.43289230, -0.30214335, -0.20984787, 0.09860471, -0.81477600, 0.79708143],
    [0.01622338, -1.69269362, -0.26077698, -0.20509248, 0.43494191, -0.17752929, 1.28809487, -1.23254732],
    [-1.18108393, -0.89543515, 17.36801757, 18.76160343, -1.23220904, -1.38879742, -0.78629468, 2.52025633],
    [0.02769849, -1.61296778, 0.28409343, 0.43469793, -0.08399074, -0.81112644, -0.59641921, 0.49062758],
    [0.44622441, -0.09817669, 0.29917600, -0.02871559, -0.51172258, 0.73528978, 1.12670072, -0.73518780],
    [-0.96953122, -0.25762838, -0.90123777, -0.05883204, 5.27770536, -0.50034674, -0.73882581, 0.61622342],
    [-0.34829580, -0.33735423, -0.40455175, -0.09496821, 0.95205055, -0.84505433, -0.72458515, 0.69158092],
    [-1.25646176, -1.93187116, -0.54164278, 0.34000258, -0.64487577, -1.21527830, -0.74357270, 0.88751043],
    [-1.12665609, -0.41708007, -0.20604936, 0.15368831, -0.86558175, -0.55387267, 1.56341430, -0.25289978],
    [-1.34489385, 1.33688856, -0.48553984, -0.21370622, 0.06193057, 0.89735456, -0.78154779, 0.62124725],
    [-0.34845371, -0.65625761, -0.60993023, -0.02386744, -0.77620495, -1.46621247, 1.00802854, -1.39330999],
    [-1.12044479, -0.01845084, -0.82639875, 0.02704104, 0.02636225, -0.67781419, -0.72458515, 0.59612809],
    [-1.54607685, -1.05488685, 0.22723986, 0.23892274, -1.16198441, -0.90949902, -1.07585478, 1.80687197],
    [0.65093417, -0.81570931, -0.07708168, -0.25080788, 0.10297094, 1.37631740, 0.75644354, -1.14211831],
    [-0.79650963, 0.77880763, -0.69317534, -0.11863483, -0.41048967, -1.94883744, -0.88123242, 0.67148559],
    [0.19671959, -1.85214532, 0.05819931, -0.17181108, 4.04649432, 0.59200842, -0.50622836, 0.72172392],
    [1.66985079, 1.09771102, 0.39470688, -0.12053744, -0.23264808, 0.58526297, -0.70559760, 0.72674776],
    [0.89933359, -1.93187116, 0.11555539, -0.02202396, -0.54729090, -1.03510697, -1.00939836, 0.90760576],
    [0.27930884, 1.89496949, -0.06621814, -0.05128440, 0.04642643, 0.65032323, 0.99853477, -1.47369133],
    [1.69580140, -1.93187116, 0.26866170, -0.11452910, 1.34330206, -0.25039039, -0.63439430, 0.47053225],
    [-0.51568511, 1.25716271, 0.07918879, 0.04346521, -0.56097102, -0.75044681, 0.33397061, 0.10379240],
    [-0.50984227, -0.97516100, -0.03982917, -0.14745629, -0.71327639, -1.13985844, -1.31794600, 1.31453628],
    [0.45275154, -1.05488685, 0.34005862, -0.10906137, 0.89824207, 0.03986376, -0.76730713, 1.25425028],
    [0.55865949, 0.53963009, -0.11953923, -0.25469262, -0.61660352, 0.24994103, -0.80528223, 0.75689076],
    [0.95602487, -1.29406439, -0.04970621, -0.10381407, -0.36580127, -0.11237641, -1.01889213, 0.93774877],
    [3.44049281, -1.61296778, 0.81882814, -0.19685326, -0.23538410, 0.68934711, -0.97142327, 0.91262960],
    [-0.57442928, 0.85853348, 0.17098707, 0.17710812, -1.14009622, 0.44370163, -0.80528223, 0.65139025],
    [-0.11131885, -1.05488685, 0.85238025, 0.71566091, -0.85098962, -0.57796081, 2.21848469, -0.79547380],
    [-0.66975697, -0.97516100, 0.02257578, -0.04042755, -0.45791410, -0.54061441, -0.71983826, 0.93774877],
    [1.26595829, 1.57606610, 0.13326125, -0.32037953, -0.85463766, -0.48764660, 0.99853477, -1.49881049],
    [-0.31492321, -0.49680592, -0.57522653, -0.16191877, -0.15147935, -2.09602545, -0.77680091, 0.50569908],
    [0.09560171, -1.05488685, 0.13607993, -0.01139009, 0.03639434, 0.48776822, -1.20876761, 1.33965545],
    [-0.00235789, -0.89543515, 0.03701636, 0.02146197, 0.45318208, 1.29078381, -1.06161412, 1.18894044],
    [-0.79003513, 1.01798517, -0.30535235, -0.17799260, -0.73151655, -0.14219786, 0.82764684, -0.75528313],
    [-0.28355087, -1.85214532, -0.49912284, -0.04865276, 1.42538280, -0.99047960, -0.68661006, 0.60617575],
    [-0.21922706, 0.14100085, -0.22249928, -0.10759499, -0.75158073, -0.46998269, 0.87986260, -1.35311932],
    [-0.96742569, -0.49680592, -0.64808686, -0.05927988, 0.21423593, 0.72814394, -1.40338997, 1.20903578],
    [0.81437561, 0.61935594, -0.05033076, -0.18060625, 0.55167896, -0.77256842, 0.84663439, -1.31795249],
    [-0.07020846, 0.30045255, -0.09122188, -0.16641149, -0.04477439, 1.09547235, -0.74831958, 0.77698609],
    [-0.26581182, -1.45351608, 6.89161043, 6.46166977, -1.03612728, -0.61772065, 1.26436043, -0.38351945],
    [-0.65612369, 0.61935594, -0.20926497, -0.16096004, -0.56735508, 0.03940938, 0.54758052, -0.15744694],
    [0.53170876, -0.41708007, 0.27646022, -0.05882751, 0.69577625, -0.48888375, 0.64726515, -1.20742815],
    [0.42943284, -0.33735423, 0.26542854, -0.16274986, -0.10587894, 0.35549232, -0.68186317, 0.27962657],
    [-0.96958386, 1.89496949, -0.91681739, -0.19685326, -0.34664910, 0.83466758, 0.63302448, -1.26269032],
    [-1.27272695, 1.01798517, -0.91497766, -0.04854721, 1.36519026, 1.82749097, -0.74831958, 0.61622342],
    [-0.79703601, 0.77880763, -0.96495616, -0.28361230, -0.23720812, 1.46611555, -0.74357270, 0.62627109],
    [-0.25486308, 1.09771102, -0.32828042, -0.24255965, -0.25727230, 0.63403621, -0.73882581, 0.66143792],
    [-1.20850841, 1.01798517, -0.29108888, -0.06644015, -0.00738205, -0.82042717, 1.95265903, -1.17728515],
    [0.71952167, 0.61935594, 0.15931321, -0.17720674, -0.44058594, -0.36793571, -0.63914119, 0.12891157],
    [-0.33861038, 0.77880763, -0.35963852, -0.03306866, -0.80903725, -1.49121262, -0.72933204, 0.57100892],
    [-1.11733914, -0.33735423, -0.88969001, -0.02851765, 2.09114877, -0.57036840, -0.73882581, 0.61119959],
    [0.18724472, 1.09771102, -0.18616508, -0.26863970, -0.08399074, 0.44392661, 0.82289996, -1.19235665],
    [-0.61332887, 1.01798517, -0.09470396, -0.01911773, -0.23264808, -0.54455120, 1.14568826, -1.38326232],
    [0.73136526, -0.73598346, 0.19060123, 0.08528996, -0.43693791, 0.08377778, 0.67574647, -1.24761882],
    [-0.79408827, 1.73551779, -0.20307980, -0.16097276, -0.40957766, -0.56122245, -0.32109977, -0.46390079],
    [-0.57563996, 0.22072670, -0.04253972, -0.08237953, -0.61021946, 0.46451947, 1.55392053, -0.89092664],
    [1.01334781, -1.05488685, -0.21615875, -0.21634091, 0.09020282, 1.76796345, 0.79916552, -1.15718981],
    [-0.34492695, -1.61296778, -0.15528179, -0.15215214, 0.66294396, 0.07409694, 1.43524836, -0.98135564],
    [-0.01762295, -1.21433854, 0.01238081, -0.22572482, 0.02180221, -0.58635365, 0.63777137, -0.28806661],
    [-0.84114678, 0.61935594, -0.92455554, -0.09390294, 1.56948009, 0.76251718, -0.72933204, 0.60617575],
    [-0.58090377, -0.17790253, -0.25718901, -0.02828000, 2.06652455, 0.47509533, -0.66287562, 0.17412607],
    [0.43080143, 0.45990424, 0.06432433, -0.17976125, -0.46338615, -0.91316345, 1.15043515, -1.33302399],
    [-1.59703058, -1.05488685, -0.15068510, 0.30924114, -1.17566454, 1.06823697, 1.82923997, -1.03661781],
    [0.73689226, -0.97516100, 0.15197900, -0.31599801, -0.56826709, 0.25746601, -0.70559760, 1.15377361],
    [-1.08391391, -1.85214532, -0.57308821, -0.25355851, -1.11912003, 1.66688121, -0.89072619, 0.82722443],
    [-0.51805382, 1.25716271, 0.16382710, 0.25759885, -0.85737368, -0.03866058, 1.76278355, -1.27776182],
    [-0.64964919, -0.17790253, -0.19384538, -0.04391264, -1.03339126, -1.93708200, -0.96192949, 0.90760576],
    [-0.34376892, -0.65625761, -0.16438476, -0.22655254, 0.69668826, -0.04044211, 1.25011977, -1.26269032],
    [-0.24391435, -0.25762838, -0.47108777, -0.08712256, 2.14495726, -0.22874640, 0.79441864, -1.22752348],
    [0.48207099, 0.61935594, 0.35554677, -0.14405047, -0.54364287, -0.68574164, 1.40676704, -0.93614114],
    [1.45566617, -1.53324193, 0.32610838, -0.18694926, 3.09709380, 0.21609418, -0.66287562, 0.25953124],
    [1.06530167, -1.21433854, -0.19422598, -0.20131617, 0.75414478, -0.72410481, -0.91446062, 0.71167626],
    [1.29727798, -1.93187116, 0.68417834, -0.05053698, 6.88192776, 1.14250479, -1.27047714, 1.16884511],
    [0.45727842, 0.14100085, 0.21737856, -0.01416173, -0.29192861, 0.19836445, 0.76593732, -1.20742815],
    [-1.07533390, 0.38017840, -0.20145869, -0.05753919, -0.19343172, 1.66822959, 0.43365524, 0.11384007],
    [0.00327439, -0.09817669, -0.01300759, -0.02343719, 0.41305372, 0.65845745, -0.82901666, 0.71167626],
    [-0.31702874, -1.05488685, -0.03024084, -0.06679121, -0.49530643, -0.68131170, 0.57606184, -0.13232778],
    [0.33731608, -0.89543515, 0.13517752, -0.23683438, -0.55549897, 1.31226761, 0.79441864, -1.16723748],
    [-0.73186998, -0.97516100, -0.23008610, -0.05180157, -0.64305176, -0.78049579, 1.69158025, -0.84068830],
    [0.29783747, 0.38017840, 0.17095986, -0.17069851, -0.22717603, 0.29136019, -0.86699176, 0.77698609]
  ]

let standardizedTargets : Vector SAMPLE_SIZE = [
    2.22300000, 2.52100000, 1.10300000, 3.17700000, 4.47400000, 2.79900000, 2.62500000, 4.12600000, 1.53200000, 2.01700000,
    2.62100000, 1.87500000, 2.36100000, 1.08900000, 3.63500000, 2.11400000, 1.18300000, 3.61700000, 0.90500000, 1.33000000,
    3.36900000, 1.40300000, 0.87500000, 3.33700000, 1.76900000, 1.77500000, 2.52400000, 1.37500000, 0.96100000, 0.99200000,
    1.00000000, 4.50000000, 0.67500000, 2.17100000, 1.65600000, 1.45500000, 4.37300000, 2.45400000, 2.70000000, 3.41200000,
    0.77000000, 1.58300000, 1.35200000, 1.73900000, 2.75900000, 5.00001000, 1.08900000, 1.13400000, 1.46900000, 4.38500000,
    5.00001000, 2.25900000, 2.08300000, 0.95000000, 1.72100000, 2.63500000, 1.10600000, 4.40900000, 1.81900000, 1.03800000,
    0.69600000, 3.22300000, 2.28800000, 1.12500000, 1.58900000, 2.25000000, 1.69600000, 0.84500000, 3.03200000, 3.83300000,
    1.12500000, 1.99000000, 1.23000000, 3.53000000, 1.36100000, 0.93600000, 2.09900000, 1.03400000, 0.94200000, 2.30400000,
    2.13200000, 2.08300000, 0.42500000, 1.40700000, 1.54200000, 0.61500000, 0.80600000, 1.20400000, 2.67500000, 1.94100000,
    2.84200000, 2.95100000, 2.60900000, 2.72000000, 0.57400000, 1.95300000, 1.24400000, 2.35800000, 1.38800000, 1.88400000
  ]

-- Verification: 100 samples, 8 features
-- Target range: [0.425, 5.000]
-- Feature means: [-0.08099349 -0.18188882  0.18631114  0.2321021   0.08770392 -0.03629437
  0.02736919 -0.001055  ]
-- Feature stds: [0.88015244 1.06536972 1.95089872 2.05823833 1.25453514 0.96749236
 1.0126208  0.97121153]
