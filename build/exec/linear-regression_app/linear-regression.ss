#!/usr/bin/scheme --program

;; @generated by Idris 0.7.0-590ff6743, Chez backend
(import (chezscheme))
(case (machine-type)
  [(i3fb ti3fb a6fb ta6fb) #f]
  [(i3le ti3le a6le ta6le tarm64le)
     (with-exception-handler (lambda(x) (load-shared-object "libc.so"))
        (lambda () (load-shared-object "libc.so.6")))]
  [(i3osx ti3osx a6osx ta6osx tarm64osx tppc32osx tppc64osx) (load-shared-object "libc.dylib")]
  [(i3nt ti3nt a6nt ta6nt) (load-shared-object "msvcrt.dll")]
  [else (load-shared-object "libc.so")])

(load-shared-object "libidris2_support.so")

(let ()
#!chezscheme

(define (blodwen-os)
  (case (machine-type)
    [(i3le ti3le a6le ta6le tarm64le) "unix"]  ; GNU/Linux
    [(i3ob ti3ob a6ob ta6ob tarm64ob) "unix"]  ; OpenBSD
    [(i3fb ti3fb a6fb ta6fb tarm64fb) "unix"]  ; FreeBSD
    [(i3nb ti3nb a6nb ta6nb tarm64nb) "unix"]  ; NetBSD
    [(i3osx ti3osx a6osx ta6osx tarm64osx tppc32osx tppc64osx) "darwin"]
    [(i3nt ti3nt a6nt ta6nt tarm64nt) "windows"]
    [else "unknown"]))

(define blodwen-lazy
  (lambda (f)
    (let ([evaluated #f] [res void])
      (lambda ()
        (if (not evaluated)
            (begin (set! evaluated #t)
                   (set! res (f))
                   (set! f void))
            (void))
        res))))

(define (blodwen-delay-lazy f)
  (weak-cons #!bwp f))

(define (blodwen-force-lazy e)
  (let ((exval (car e)))
    (if (bwp-object? exval)
      (let ((val ((cdr e))))
        (begin (set-car! e val) val))
      exval)))

(define (blodwen-toSignedInt x bits)
  (if (logbit? bits x)
      (logor x (ash -1 bits))
      (logand x (sub1 (ash 1 bits)))))

(define (blodwen-toUnsignedInt x bits)
  (logand x (sub1 (ash 1 bits))))

(define (blodwen-euclidDiv a b)
  (let ((q (quotient a b))
        (r (remainder a b)))
    (if (< r 0)
      (if (> b 0) (- q 1) (+ q 1))
      q)))

(define (blodwen-euclidMod a b)
  (let ((r (remainder a b)))
    (if (< r 0)
      (if (> b 0) (+ r b) (- r b))
      r)))

; flonum constants

(define (blodwen-calcFlonumUnitRoundoff)
  (let loop [(uro 1.0)]
    (if (fl= 1.0 (fl+ 1.0 uro))
      uro
      (loop (fl/ uro 2.0)))))

(define (blodwen-calcFlonumEpsilon)
  (fl* (blodwen-calcFlonumUnitRoundoff) 2.0))

(define (blodwen-flonumNaN)
  +nan.0)

(define (blodwen-flonumInf)
  +inf.0)

; Bits

(define bu+ (lambda (x y bits) (blodwen-toUnsignedInt (+ x y) bits)))
(define bu- (lambda (x y bits) (blodwen-toUnsignedInt (- x y) bits)))
(define bu* (lambda (x y bits) (blodwen-toUnsignedInt (* x y) bits)))
(define bu/ (lambda (x y bits) (blodwen-toUnsignedInt (quotient x y) bits)))

(define bs+ (lambda (x y bits) (blodwen-toSignedInt (+ x y) bits)))
(define bs- (lambda (x y bits) (blodwen-toSignedInt (- x y) bits)))
(define bs* (lambda (x y bits) (blodwen-toSignedInt (* x y) bits)))
(define bs/ (lambda (x y bits) (blodwen-toSignedInt (blodwen-euclidDiv x y) bits)))

(define (integer->bits8 x) (logand x (sub1 (ash 1 8))))
(define (integer->bits16 x) (logand x (sub1 (ash 1 16))))
(define (integer->bits32 x) (logand x (sub1 (ash 1 32))))
(define (integer->bits64 x) (logand x (sub1 (ash 1 64))))

(define (bits16->bits8 x) (logand x (sub1 (ash 1 8))))
(define (bits32->bits8 x) (logand x (sub1 (ash 1 8))))
(define (bits64->bits8 x) (logand x (sub1 (ash 1 8))))
(define (bits32->bits16 x) (logand x (sub1 (ash 1 16))))
(define (bits64->bits16 x) (logand x (sub1 (ash 1 16))))
(define (bits64->bits32 x) (logand x (sub1 (ash 1 32))))

(define (blodwen-bits-shl-signed x y bits) (blodwen-toSignedInt (ash x y) bits))

(define (blodwen-bits-shl x y bits) (logand (ash x y) (sub1 (ash 1 bits))))

(define blodwen-shl (lambda (x y) (ash x y)))
(define blodwen-shr (lambda (x y) (ash x (- y))))
(define blodwen-and (lambda (x y) (logand x y)))
(define blodwen-or (lambda (x y) (logor x y)))
(define blodwen-xor (lambda (x y) (logxor x y)))

(define cast-num
  (lambda (x)
    (if (number? x) x 0)))
(define destroy-prefix
  (lambda (x)
    (cond
      ((equal? x "") "")
      ((equal? (string-ref x 0) #\#) "")
      (else x))))

(define exact-floor
  (lambda (x)
    (inexact->exact (floor x))))

(define exact-truncate
  (lambda (x)
    (inexact->exact (truncate x))))

(define exact-truncate-boundedInt
  (lambda (x y)
    (blodwen-toSignedInt (exact-truncate x) y)))

(define exact-truncate-boundedUInt
  (lambda (x y)
    (blodwen-toUnsignedInt (exact-truncate x) y)))

(define cast-char-boundedInt
  (lambda (x y)
    (blodwen-toSignedInt (char->integer x) y)))

(define cast-char-boundedUInt
  (lambda (x y)
    (blodwen-toUnsignedInt (char->integer x) y)))

(define cast-string-int
  (lambda (x)
    (exact-truncate (cast-num (string->number (destroy-prefix x))))))

(define cast-string-boundedInt
  (lambda (x y)
    (blodwen-toSignedInt (cast-string-int x) y)))

(define cast-string-boundedUInt
  (lambda (x y)
    (blodwen-toUnsignedInt (cast-string-int x) y)))

(define cast-int-char
  (lambda (x)
    (if (or
          (and (>= x 0) (<= x #xd7ff))
          (and (>= x #xe000) (<= x #x10ffff)))
        (integer->char x)
        (integer->char 0))))

(define cast-string-double
  (lambda (x)
    (exact->inexact (cast-num (string->number (destroy-prefix x))))))


(define (string-concat xs) (apply string-append xs))
(define (string-unpack s) (string->list s))
(define (string-pack xs) (list->string xs))

(define string-cons (lambda (x y) (string-append (string x) y)))
(define string-reverse (lambda (x)
  (list->string (reverse (string->list x)))))
(define (string-substr off len s)
    (let* ((l (string-length s))
          (b (max 0 off))
          (x (max 0 len))
          (end (min l (+ b x))))
          (if (> b l)
              ""
              (substring s b end))))

(define (blodwen-string-iterator-new s)
  0)

(define (blodwen-string-iterator-to-string _ s ofs f)
  (f (substring s ofs (string-length s))))

(define (blodwen-string-iterator-next s ofs)
  (if (>= ofs (string-length s))
      '() ; EOF
      (cons (string-ref s ofs) (+ ofs 1))))

(define either-left
  (lambda (x)
    (vector 0 x)))

(define either-right
  (lambda (x)
    (vector 1 x)))

(define blodwen-error-quit
  (lambda (msg)
    (display msg)
    (newline)
    (exit 1)))

(define (blodwen-get-line p)
    (if (port? p)
        (let ((str (get-line p)))
            (if (eof-object? str)
                ""
                str))
        void))

(define (blodwen-get-char p)
    (if (port? p)
        (let ((chr (get-char p)))
            (if (eof-object? chr)
                #\nul
                chr))
        void))

;; Buffers

(define (blodwen-new-buffer size)
  (make-bytevector size 0))

(define (blodwen-buffer-size buf)
  (bytevector-length buf))

(define (blodwen-buffer-setbyte buf loc val)
  (bytevector-u8-set! buf loc val))

(define (blodwen-buffer-getbyte buf loc)
  (bytevector-u8-ref buf loc))

(define (blodwen-buffer-setbits16 buf loc val)
  (bytevector-u16-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getbits16 buf loc)
  (bytevector-u16-ref buf loc (native-endianness)))

(define (blodwen-buffer-setbits32 buf loc val)
  (bytevector-u32-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getbits32 buf loc)
  (bytevector-u32-ref buf loc (native-endianness)))

(define (blodwen-buffer-setbits64 buf loc val)
  (bytevector-u64-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getbits64 buf loc)
  (bytevector-u64-ref buf loc (native-endianness)))

(define (blodwen-buffer-setint8 buf loc val)
  (bytevector-s8-set! buf loc val))

(define (blodwen-buffer-getint8 buf loc)
  (bytevector-s8-ref buf loc))

(define (blodwen-buffer-setint16 buf loc val)
  (bytevector-s16-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getint16 buf loc)
  (bytevector-s16-ref buf loc (native-endianness)))

(define (blodwen-buffer-setint32 buf loc val)
  (bytevector-s32-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getint32 buf loc)
  (bytevector-s32-ref buf loc (native-endianness)))

(define (blodwen-buffer-setint buf loc val)
  (bytevector-s64-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getint buf loc)
  (bytevector-s64-ref buf loc (native-endianness)))

(define (blodwen-buffer-setint64 buf loc val)
  (bytevector-s64-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getint64 buf loc)
  (bytevector-s64-ref buf loc (native-endianness)))

(define (blodwen-buffer-setdouble buf loc val)
  (bytevector-ieee-double-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getdouble buf loc)
  (bytevector-ieee-double-ref buf loc (native-endianness)))

(define (blodwen-stringbytelen str)
  (bytevector-length (string->utf8 str)))

(define (blodwen-buffer-setstring buf loc val)
  (let* [(strvec (string->utf8 val))
         (len (bytevector-length strvec))]
    (bytevector-copy! strvec 0 buf loc len)))

(define (blodwen-buffer-getstring buf loc len)
  (let [(newvec (make-bytevector len))]
    (bytevector-copy! buf loc newvec 0 len)
    (utf8->string newvec)))

(define (blodwen-buffer-copydata buf start len dest loc)
  (bytevector-copy! buf start dest loc len))

;; Threads

(define-record thread-handle (semaphore))

(define (blodwen-thread proc)
  (let [(sema (blodwen-make-semaphore 0))]
    (fork-thread (lambda () (proc (vector 0)) (blodwen-semaphore-post sema)))
    (make-thread-handle sema)
    ))

(define (blodwen-thread-wait handle)
  (blodwen-semaphore-wait (thread-handle-semaphore handle)))

;; Thread mailboxes

(define blodwen-thread-data
  (make-thread-parameter #f))

(define (blodwen-get-thread-data ty)
  (blodwen-thread-data))

(define (blodwen-set-thread-data ty a)
  (blodwen-thread-data a))

;; Semaphore

(define-record semaphore (box mutex condition))

(define (blodwen-make-semaphore init)
  (make-semaphore (box init) (make-mutex) (make-condition)))

(define (blodwen-semaphore-post sema)
  (with-mutex (semaphore-mutex sema)
    (let [(sema-box (semaphore-box sema))]
      (set-box! sema-box (+ (unbox sema-box) 1))
      (condition-signal (semaphore-condition sema))
    )))

(define (blodwen-semaphore-wait sema)
  (with-mutex (semaphore-mutex sema)
    (let [(sema-box (semaphore-box sema))]
      (when (= (unbox sema-box) 0)
        (condition-wait (semaphore-condition sema) (semaphore-mutex sema)))
      (set-box! sema-box (- (unbox sema-box) 1))
      )))

;; Barrier

(define-record barrier (count-box num-threads mutex cond))

(define (blodwen-make-barrier num-threads)
  (make-barrier (box 0) num-threads (make-mutex) (make-condition)))

(define (blodwen-barrier-wait barrier)
  (let [(count-box (barrier-count-box barrier))
        (num-threads (barrier-num-threads barrier))
        (mutex (barrier-mutex barrier))
        (condition (barrier-cond barrier))]
    (with-mutex mutex
    (let* [(count-old (unbox count-box))
           (count-new (+ count-old 1))]
      (set-box! count-box count-new)
      (if (= count-new num-threads)
          (condition-broadcast condition)
          (condition-wait condition mutex))
      ))))

;; Channel
; With thanks to Alain Zscheile (@zseri) for help with understanding condition
; variables, and figuring out where the problems were and how to solve them.

(define-record channel (read-mut read-cv read-box val-cv val-box))

(define (blodwen-make-channel ty)
  (make-channel
    (make-mutex)
    (make-condition)
    (box #t)
    (make-condition)
    (box '())
    ))

; block on the read status using read-cv until the value has been read
(define (channel-put-while-helper chan)
  (let ([read-mut (channel-read-mut chan)]
        [read-box (channel-read-box chan)]
        [read-cv  (channel-read-cv  chan)]
        )
    (if (unbox read-box)
      (void)    ; val has been read, so everything is fine
      (begin    ; otherwise, block/spin with cv
        (condition-wait read-cv read-mut)
        (channel-put-while-helper chan)
        )
      )))

(define (blodwen-channel-put ty chan val)
  (with-mutex (channel-read-mut chan)
    (channel-put-while-helper chan)
    (let ([read-box (channel-read-box chan)]
          [val-box  (channel-val-box  chan)]
          )
      (set-box! val-box val)
      (set-box! read-box #f)
      ))
  (condition-signal (channel-val-cv chan))
  )

; block on the value until it has been set
(define (channel-get-while-helper chan)
  (let ([read-mut (channel-read-mut chan)]
        [read-box (channel-read-box chan)]
        [val-cv   (channel-val-cv   chan)]
        )
    (if (unbox read-box)
      (begin
        (condition-wait val-cv read-mut)
        (channel-get-while-helper chan)
        )
      (void)
      )))

(define (blodwen-channel-get ty chan)
  (mutex-acquire (channel-read-mut chan))
  (channel-get-while-helper chan)
  (let* ([val-box  (channel-val-box  chan)]
         [read-box (channel-read-box chan)]
         [read-cv  (channel-read-cv  chan)]
         [the-val  (unbox val-box)]
         )
    (set-box! val-box '())
    (set-box! read-box #t)
    (mutex-release (channel-read-mut chan))
    (condition-signal read-cv)
    the-val))

(define (blodwen-channel-get-non-blocking ty chan)
  (if (mutex-acquire (channel-read-mut chan) #f)
    (let* ([val-box  (channel-val-box  chan)]
           [read-box (channel-read-box chan)]
           [read-cv  (channel-read-cv  chan)]
           [the-val  (unbox val-box)]
          )
      (if (null? the-val)
          (begin
           (mutex-release (channel-read-mut chan))
           '())
          (begin
            (set-box! val-box '())
            (set-box! read-box #t)
            (mutex-release (channel-read-mut chan))
            (condition-signal read-cv)
            (box the-val))
      ))
  '()))

(define (blodwen-channel-get-with-timeout ty chan timeout)
  (let loop ()
    (let* ([sec (div timeout 1000)])
      (if (mutex-acquire (channel-read-mut chan) #f)
          (let* ([val-box  (channel-val-box chan)]
                 [val-cv   (channel-val-cv  chan)]
                 [the-val  (unbox val-box)])
            (if (null? the-val)
                (begin
                  ;; Wait for the condition timeout
                  (condition-wait val-cv (channel-read-mut chan) (make-time 'time-duration 0 sec))
                  (let* ([the-val (unbox val-box)]) ; Check again after wait
                    (if (null? the-val)
                        (begin
                          (mutex-release (channel-read-mut chan))
                          '()) ; Still empty after timeout
                        (let* ([read-box (channel-read-box chan)]
                               [read-cv  (channel-read-cv chan)])
                          ;; Value now available
                          (set-box! val-box '())
                          (set-box! read-box #t)
                          (mutex-release (channel-read-mut chan))
                          (condition-signal read-cv)
                          (box the-val)))))
                (let* ([read-box (channel-read-box chan)]
                       [read-cv  (channel-read-cv chan)])
                  ;; Value available immediately
                  (set-box! val-box '())
                  (set-box! read-box #t)
                  (mutex-release (channel-read-mut chan))
                  (condition-signal read-cv)
                  (box the-val))))
          loop)))) ; Failed to acquire mutex

;; Mutex

(define (blodwen-make-mutex)
  (make-mutex))
(define (blodwen-mutex-acquire mutex)
  (mutex-acquire mutex))
(define (blodwen-mutex-release mutex)
  (mutex-release mutex))

;; Condition variable

(define (blodwen-make-condition)
  (make-condition))
(define (blodwen-condition-wait condition mutex)
  (condition-wait condition mutex))
(define (blodwen-condition-wait-timeout condition mutex timeout)
  (let* [(sec (div timeout 1000000))
         (micro (mod timeout 1000000))]
    (condition-wait condition mutex (make-time 'time-duration (* 1000 micro) sec))))
(define (blodwen-condition-signal condition)
  (condition-signal condition))
(define (blodwen-condition-broadcast condition)
  (condition-broadcast condition))

;; Future

(define-record future-internal (result ready mutex signal))
(define (blodwen-make-future ty work)
  (let ([future (make-future-internal #f #f (make-mutex) (make-condition))])
    (fork-thread (lambda ()
      (let ([result (work '())])
        (with-mutex (future-internal-mutex future)
          (set-future-internal-result! future result)
          (set-future-internal-ready! future #t)
          (condition-broadcast (future-internal-signal future))))))
    future))
(define (blodwen-await-future ty future)
  (let ([mutex (future-internal-mutex future)])
    (with-mutex mutex
      (if (not (future-internal-ready future))
          (condition-wait (future-internal-signal future) mutex))
      (future-internal-result future))))

(define (blodwen-sleep s) (sleep (make-time 'time-duration 0 s)))
(define (blodwen-usleep s)
  (let ((sec (div s 1000000))
        (micro (mod s 1000000)))
       (sleep (make-time 'time-duration (* 1000 micro) sec))))

(define (blodwen-clock-time-utc) (current-time 'time-utc))
(define (blodwen-clock-time-monotonic) (current-time 'time-monotonic))
(define (blodwen-clock-time-duration) (current-time 'time-duration))
(define (blodwen-clock-time-process) (current-time 'time-process))
(define (blodwen-clock-time-thread) (current-time 'time-thread))
(define (blodwen-clock-time-gccpu) (current-time 'time-collector-cpu))
(define (blodwen-clock-time-gcreal) (current-time 'time-collector-real))
(define (blodwen-is-time? clk) (if (time? clk) 1 0))
(define (blodwen-clock-second time) (time-second time))
(define (blodwen-clock-nanosecond time) (time-nanosecond time))

(define (blodwen-arg-count)
  (length (command-line)))

(define (blodwen-arg n)
  (if (< n (length (command-line))) (list-ref (command-line) n) ""))

(define (blodwen-hasenv var)
  (if (eq? (getenv var) #f) 0 1))

;; Randoms
(define random-seed-register 0)
(define (initialize-random-seed-once)
  (if (= (virtual-register random-seed-register) 0)
      (let ([seed (time-nanosecond (current-time))])
        (set-virtual-register! random-seed-register seed)
        (random-seed seed))))

(define (blodwen-random-seed seed)
  (set-virtual-register! random-seed-register seed)
  (random-seed seed))
(define blodwen-random
  (case-lambda
    ;; no argument, pick a real value from [0, 1.0)
    [() (begin
          (initialize-random-seed-once)
          (random 1.0))]
    ;; single argument k, pick an integral value from [0, k)
    [(k)
      (begin
        (initialize-random-seed-once)
        (if (> k 0)
              (random k)
              (assertion-violationf 'blodwen-random "invalid range argument ~a" k)))]))

;; For finalisers

(define blodwen-finaliser (make-guardian))
(define (blodwen-register-object obj proc)
  (let [(x (cons obj proc))]
       (blodwen-finaliser x)
       x))
(define blodwen-run-finalisers
  (lambda ()
    (let run ()
      (let ([x (blodwen-finaliser)])
        (when x
          (((cdr x) (car x)) 'erased)
          (run))))))

;; For creating and reading back scheme objects

; read a scheme string and evaluate it, returning 'Just result' on success
; TODO: catch exception!
(define (blodwen-eval-scheme str)
  (guard
     (x [#t '()]) ; Nothing on failure
     (box (eval (read (open-input-string str)))))
  ); box == Just

(define (blodwen-eval-okay obj)
  (if (null? obj)
      0
      1))

(define (blodwen-get-eval-result obj)
  (unbox obj))

(define (blodwen-debug-scheme obj)
  (display obj) (newline))

(define (blodwen-is-number obj)
  (if (number? obj) 1 0))

(define (blodwen-is-integer obj)
  (if (and (number? obj) (exact? obj)) 1 0))

(define (blodwen-is-float obj)
  (if (flonum? obj) 1 0))

(define (blodwen-is-char obj)
  (if (char? obj) 1 0))

(define (blodwen-is-string obj)
  (if (string? obj) 1 0))

(define (blodwen-is-procedure obj)
  (if (procedure? obj) 1 0))

(define (blodwen-is-symbol obj)
  (if (symbol? obj) 1 0))

(define (blodwen-is-vector obj)
  (if (vector? obj) 1 0))

(define (blodwen-is-nil obj)
  (if (null? obj) 1 0))

(define (blodwen-is-pair obj)
  (if (pair? obj) 1 0))

(define (blodwen-is-box obj)
  (if (box? obj) 1 0))

(define (blodwen-make-symbol str)
  (string->symbol str))

; The below rely on checking that the objects are the right type first.

(define (blodwen-vector-ref obj i)
  (vector-ref obj i))

(define (blodwen-vector-length obj)
  (vector-length obj))

(define (blodwen-vector-list obj)
  (vector->list obj))

(define (blodwen-unbox obj)
  (unbox obj))

(define (blodwen-apply obj arg)
  (obj arg))

(define (blodwen-force obj)
  (obj))

(define (blodwen-read-symbol sym)
  (symbol->string sym))

(define (blodwen-id x) x)
(define System-prim__system (lambda (farg-0 farg-1) ((foreign-procedure "idris2_system" (string) int) farg-0)))
(define System-prim__exit (lambda (farg-0 farg-1) ((foreign-procedure "exit" (int) void) farg-0)))
(define PreludeC-45Types-fastUnpack (lambda (farg-0) (string-unpack farg-0)))
(define PreludeC-45Types-fastPack (lambda (farg-0) (string-pack farg-0)))
(define PreludeC-45Types-fastConcat (lambda (farg-0) (string-concat farg-0)))
(define PreludeC-45IO-prim__putStr (lambda (farg-0 farg-1) ((foreign-procedure "idris2_putStr" (string) void) farg-0)))
(define PreludeC-45IO-prim__getString (lambda (farg-0) ((foreign-procedure "idris2_getString" (void*) string) farg-0)))
(define PrimIO-prim__nullAnyPtr (lambda (farg-0) ((foreign-procedure "idris2_isNull" (void*) int) farg-0)))
(define SystemC-45FileC-45ReadWrite-prim__writeLine (lambda (farg-0 farg-1 farg-2) ((foreign-procedure "idris2_writeLine" (void* string) int) farg-0 farg-1)))
(define SystemC-45FileC-45ReadWrite-prim__seekLine (lambda (farg-0 farg-1) ((foreign-procedure "idris2_seekLine" (void*) int) farg-0)))
(define SystemC-45FileC-45ReadWrite-prim__readLine (lambda (farg-0 farg-1) ((foreign-procedure "idris2_readLine" (void*) void*) farg-0)))
(define SystemC-45FileC-45ReadWrite-prim__eof (lambda (farg-0 farg-1) ((foreign-procedure "idris2_eof" (void*) int) farg-0)))
(define SystemC-45FFI-prim__free (lambda (farg-0 farg-1) ((foreign-procedure "idris2_free" (void*) void) farg-0)))
(define SystemC-45FileC-45Error-prim__fileErrno (lambda (farg-0) ((foreign-procedure "idris2_fileErrno" () int) )))
(define SystemC-45FileC-45Error-prim__error (lambda (farg-0 farg-1) ((foreign-procedure "idris2_fileError" (void*) int) farg-0)))
(define SystemC-45Errno-prim__strerror (lambda (farg-0 farg-1) ((foreign-procedure "idris2_strerror" (int) string) farg-0)))
(define SystemC-45FileC-45Handle-prim__open (lambda (farg-0 farg-1 farg-2) ((foreign-procedure "idris2_openFile" (string string) void*) farg-0 farg-1)))
(define SystemC-45FileC-45Handle-prim__close (lambda (farg-0 farg-1) ((foreign-procedure "idris2_closeFile" (void*) void) farg-0)))
(define PreludeC-45IO-u--map_Functor_IO (lambda (arg-2 arg-3 ext-0) (let ((act-2 (arg-3 ext-0))) (arg-2 act-2))))
(define csegen-13(delay (cons (vector (vector (lambda (u--b) (lambda (u--a) (lambda (u--func) (lambda (arg-8945) (lambda (eta-0) (PreludeC-45IO-u--map_Functor_IO u--func arg-8945 eta-0)))))) (lambda (u--a) (lambda (arg-9991) (lambda (eta-0) arg-9991))) (lambda (u--b) (lambda (u--a) (lambda (arg-9997) (lambda (arg-10004) (lambda (world-4) (let ((act-5 (arg-9997 world-4))) (let ((act-3 (arg-10004 world-4))) (act-5 act-3))))))))) (lambda (u--b) (lambda (u--a) (lambda (arg-10477) (lambda (arg-10480) (lambda (world-0) (let ((act-1 (arg-10477 world-0))) ((arg-10480 act-1) world-0))))))) (lambda (u--a) (lambda (arg-10491) (lambda (world-0) (let ((act-1 (arg-10491 world-0))) (act-1 world-0)))))) (lambda (u--a) (lambda (arg-13090) arg-13090)))))
(define csegen-15 (lambda (eta-0) (lambda (eta-1) (- eta-0 eta-1))))
(define csegen-20 (vector (lambda (arg-5944) (lambda (arg-5947) (+ arg-5944 arg-5947))) (lambda (arg-5954) (lambda (arg-5957) (* arg-5954 arg-5957))) (lambda (arg-5964) (exact->inexact arg-5964))))
(define DataC-45Vect-u--foldl_Foldable_C-40VectC-32C-36nC-41 (lambda (arg-3 arg-4 arg-5) (if (null? arg-5) arg-4 (let ((e-3 (car arg-5))) (let ((e-4 (cdr arg-5))) (DataC-45Vect-u--foldl_Foldable_C-40VectC-32C-36nC-41 arg-3 ((arg-3 arg-4) e-3) e-4))))))
(define DataC-45Vect-u--foldMap_Foldable_C-40VectC-32C-36nC-41 (lambda (arg-3 arg-4 ext-0) (DataC-45Vect-u--foldl_Foldable_C-40VectC-32C-36nC-41 (lambda (u--acc) (lambda (u--elem) (let ((e-1 (car arg-3))) ((e-1 u--acc) (arg-4 u--elem))))) (let ((e-2 (cdr arg-3))) e-2) ext-0)))
(define PreludeC-45Basics-flip (lambda (arg-3 ext-0 ext-1) ((arg-3 ext-1) ext-0)))
(define DataC-45Vect-u--foldlM_Foldable_C-40VectC-32C-36nC-41 (lambda (arg-4 arg-5 arg-6 ext-0) (DataC-45Vect-u--foldl_Foldable_C-40VectC-32C-36nC-41 (lambda (u--ma) (lambda (u--b) (let ((e-2 (vector-ref arg-4 1))) ((((e-2 'erased) 'erased) u--ma) (lambda (eta-0) (PreludeC-45Basics-flip arg-5 u--b eta-0)))))) (let ((e-1 (vector-ref arg-4 0))) (let ((e-5 (vector-ref e-1 1))) ((e-5 'erased) arg-6))) ext-0)))
(define DataC-45Vect-foldrImpl (lambda (arg-3 arg-4 arg-5 arg-6) (if (null? arg-6) (arg-5 arg-4) (let ((e-3 (car arg-6))) (let ((e-4 (cdr arg-6))) (DataC-45Vect-foldrImpl arg-3 arg-4 (lambda (eta-0) (arg-5 ((arg-3 e-3) eta-0))) e-4))))))
(define DataC-45Vect-u--foldr_Foldable_C-40VectC-32C-36nC-41 (lambda (arg-3 arg-4 arg-5) (DataC-45Vect-foldrImpl arg-3 arg-4 (lambda (eta-0) eta-0) arg-5)))
(define DataC-45Vect-u--null_Foldable_C-40VectC-32C-36nC-41 (lambda (arg-2) (if (null? arg-2) 1 0)))
(define csegen-66 (lambda (eta-0) (lambda (eta-1) (cons eta-0 eta-1))))
(define DataC-45Vect-u--toList_Foldable_C-40VectC-32C-36nC-41 (lambda (ext-0) (DataC-45Vect-u--foldr_Foldable_C-40VectC-32C-36nC-41 csegen-66 '() ext-0)))
(define csegen-36 (vector (lambda (u--acc) (lambda (u--elem) (lambda (u--func) (lambda (u--init) (lambda (u--input) (DataC-45Vect-u--foldr_Foldable_C-40VectC-32C-36nC-41 u--func u--init u--input)))))) (lambda (u--elem) (lambda (u--acc) (lambda (u--func) (lambda (u--init) (lambda (u--input) (DataC-45Vect-u--foldl_Foldable_C-40VectC-32C-36nC-41 u--func u--init u--input)))))) (lambda (u--elem) (lambda (arg-10980) (DataC-45Vect-u--null_Foldable_C-40VectC-32C-36nC-41 arg-10980))) (lambda (u--elem) (lambda (u--acc) (lambda (u--m) (lambda (i_con-0) (lambda (u--funcM) (lambda (u--init) (lambda (u--input) (DataC-45Vect-u--foldlM_Foldable_C-40VectC-32C-36nC-41 i_con-0 u--funcM u--init u--input)))))))) (lambda (u--elem) (lambda (arg-11009) (DataC-45Vect-u--toList_Foldable_C-40VectC-32C-36nC-41 arg-11009))) (lambda (u--a) (lambda (u--m) (lambda (i_con-0) (lambda (u--f) (lambda (arg-11023) (DataC-45Vect-u--foldMap_Foldable_C-40VectC-32C-36nC-41 i_con-0 u--f arg-11023))))))))
(define csegen-38 (lambda (eta-0) (lambda (eta-1) (* eta-0 eta-1))))
(define PreludeC-45Types-u--C-60C-42C-62_Applicative_Maybe (lambda (arg-2 arg-3) (if (null? arg-2) '() (let ((e-1 (unbox arg-2))) (if (null? arg-3) '() (let ((e-3 (unbox arg-3))) (box (e-1 e-3))))))))
(define PreludeC-45Types-u--map_Functor_Maybe (lambda (arg-2 arg-3) (if (null? arg-3) '() (let ((e-1 (unbox arg-3))) (box (arg-2 e-1))))))
(define csegen-46 (vector (lambda (u--b) (lambda (u--a) (lambda (u--func) (lambda (arg-8945) (PreludeC-45Types-u--map_Functor_Maybe u--func arg-8945))))) (lambda (u--a) (lambda (arg-9991) (box arg-9991))) (lambda (u--b) (lambda (u--a) (lambda (arg-9997) (lambda (arg-10004) (PreludeC-45Types-u--C-60C-42C-62_Applicative_Maybe arg-9997 arg-10004)))))))
(define u--prim__sub_Integer (lambda (arg-0 arg-1) (- arg-0 arg-1)))
(define LinearRegression-FEATURES 8)
(define DataC-45Vect-index (lambda (arg-2 arg-3) (cond ((equal? arg-2 0) (let ((e-5 (car arg-3))) e-5))(else (let ((e-2 (- arg-2 1))) (let ((e-10 (cdr arg-3))) (DataC-45Vect-index e-2 e-10)))))))
(define PreludeC-45InterfacesC-45NumC-45Semigroup-u--C-60C-43C-62_Semigroup_AdditiveC-36a (lambda (arg-1 ext-0 ext-1) (let ((e-1 (vector-ref arg-1 0))) ((e-1 ext-0) ext-1))))
(define PreludeC-45InterfacesC-45NumC-45Monoid-u--neutral_Monoid_AdditiveC-36a (lambda (arg-1) (let ((e-3 (vector-ref arg-1 2))) (e-3 0))))
(define PreludeC-45Interfaces-sum (lambda (arg-2 arg-3 ext-0) (let ((e-6 (vector-ref arg-3 5))) (((((e-6 'erased) 'erased) (cons (lambda (arg-8530) (lambda (arg-8533) (PreludeC-45InterfacesC-45NumC-45Semigroup-u--C-60C-43C-62_Semigroup_AdditiveC-36a arg-2 arg-8530 arg-8533))) (PreludeC-45InterfacesC-45NumC-45Monoid-u--neutral_Monoid_AdditiveC-36a arg-2))) (lambda (eta-0) eta-0)) ext-0))))
(define DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 (lambda (arg-4 arg-5 arg-6) (if (null? arg-5) '() (let ((e-3 (car arg-5))) (let ((e-4 (cdr arg-5))) (let ((e-8 (car arg-6))) (let ((e-9 (cdr arg-6))) (cons ((arg-4 e-3) e-8) (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 arg-4 e-4 e-9)))))))))
(define LinearRegression-predict (lambda (arg-0 arg-1 arg-2) (let ((u--dotProduct (PreludeC-45Interfaces-sum csegen-20 csegen-36 (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 csegen-38 arg-0 arg-1)))) (+ u--dotProduct arg-2))))
(define DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (arg-3 arg-4) (if (null? arg-4) '() (let ((e-3 (car arg-4))) (let ((e-4 (cdr arg-4))) (cons (arg-3 e-3) (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 arg-3 e-4)))))))
(define LinearRegression-predictBatch (lambda (arg-0 arg-1 arg-2) (let ((e-2 (car arg-2))) (let ((e-3 (cdr arg-2))) (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (u--input) (LinearRegression-predict u--input e-2 e-3)) arg-1)))))
(define System-system (lambda (arg-1 arg-2) (let ((e-2 (cdr arg-1))) ((e-2 'erased) (lambda (eta-0) (System-prim__system arg-2 eta-0))))))
(define SystemC-45FileC-45Support-ok (lambda (arg-3 arg-4) (let ((e-1 (car arg-3))) (let ((e-5 (vector-ref e-1 0))) (let ((e-7 (vector-ref e-5 1))) ((e-7 'erased) (vector 1 arg-4)))))))
(define SystemC-45FileC-45Error-returnError (lambda (arg-2) (let ((e-1 (car arg-2))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-2))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45Error-prim__fileErrno eta-0))))) (lambda (u--err) (let ((e-7 (car arg-2))) (let ((e-10 (vector-ref e-7 0))) (let ((e-12 (vector-ref e-10 1))) ((e-12 'erased) (vector 0 (cond ((equal? u--err 0) (vector 1 )) ((equal? u--err 1) (vector 2 )) ((equal? u--err 2) (vector 3 )) ((equal? u--err 3) (vector 4 )) ((equal? u--err 4) (vector 5 ))(else (vector 0 (bs- u--err 5 63)))))))))))))))
(define PreludeC-45EqOrd-u--C-61C-61_Eq_Int (lambda (arg-0 arg-1) (let ((sc0 (or (and (= arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define SystemC-45FileC-45ReadWrite-fPutStr (lambda (arg-1 arg-2 arg-3) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-1))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45ReadWrite-prim__writeLine arg-2 arg-3 eta-0))))) (lambda (u--res) (let ((sc1 (PreludeC-45EqOrd-u--C-61C-61_Eq_Int u--res (blodwen-toSignedInt 0 63)))) (cond ((equal? sc1 1) (SystemC-45FileC-45Error-returnError arg-1)) (else (SystemC-45FileC-45Support-ok arg-1 'erased))))))))))
(define SystemC-45FileC-45Handle-closeFile (lambda (arg-1 arg-2) (let ((e-2 (cdr arg-1))) ((e-2 'erased) (lambda (eta-0) (SystemC-45FileC-45Handle-prim__close arg-2 eta-0))))))
(define PreludeC-45InterfacesC-45BoolC-45Semigroup-u--C-60C-43C-62_Semigroup_AnyBool (lambda (arg-0 arg-1) (cond ((equal? arg-0 1) 1) (else arg-1))))
(define PreludeC-45Types-elemBy (lambda (arg-2 arg-3 arg-4 ext-0) (let ((e-6 (vector-ref arg-2 5))) (((((e-6 'erased) 'erased) (cons (lambda (arg-8530) (lambda (arg-8533) (PreludeC-45InterfacesC-45BoolC-45Semigroup-u--C-60C-43C-62_Semigroup_AnyBool arg-8530 arg-8533))) 0)) (arg-3 arg-4)) ext-0))))
(define PreludeC-45Types-elem (lambda (arg-2 arg-3 ext-1 ext-0) (PreludeC-45Types-elemBy arg-2 (lambda (eta-0) (lambda (eta-1) (let ((e-1 (car arg-3))) ((e-1 eta-0) eta-1)))) ext-1 ext-0)))
(define SystemC-45Info-os (blodwen-os))
(define PreludeC-45EqOrd-u--C-61C-61_Eq_String (lambda (arg-0 arg-1) (let ((sc0 (or (and (string=? arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45EqOrd-u--C-47C-61_Eq_String (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-61C-61_Eq_String arg-0 arg-1))) (cond ((equal? sc0 1) 0) (else 1)))))
(define PreludeC-45Types-u--foldl_Foldable_List (lambda (arg-2 arg-3 arg-4) (if (null? arg-4) arg-3 (let ((e-2 (car arg-4))) (let ((e-3 (cdr arg-4))) (PreludeC-45Types-u--foldl_Foldable_List arg-2 ((arg-2 arg-3) e-2) e-3))))))
(define PreludeC-45Types-u--foldMap_Foldable_List (lambda (arg-2 arg-3 ext-0) (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (u--elem) (let ((e-1 (car arg-2))) ((e-1 u--acc) (arg-3 u--elem))))) (let ((e-2 (cdr arg-2))) e-2) ext-0)))
(define PreludeC-45Types-u--foldlM_Foldable_List (lambda (arg-3 arg-4 arg-5 ext-0) (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--ma) (lambda (u--b) (let ((e-2 (vector-ref arg-3 1))) ((((e-2 'erased) 'erased) u--ma) (lambda (eta-0) (PreludeC-45Basics-flip arg-4 u--b eta-0)))))) (let ((e-1 (vector-ref arg-3 0))) (let ((e-5 (vector-ref e-1 1))) ((e-5 'erased) arg-5))) ext-0)))
(define PreludeC-45Types-u--foldr_Foldable_List (lambda (arg-2 arg-3 arg-4) (if (null? arg-4) arg-3 (let ((e-2 (car arg-4))) (let ((e-3 (cdr arg-4))) ((arg-2 e-2) (PreludeC-45Types-u--foldr_Foldable_List arg-2 arg-3 e-3)))))))
(define PreludeC-45Types-u--null_Foldable_List (lambda (arg-1) (if (null? arg-1) 1 0)))
(define SystemC-45Info-isWindows (PreludeC-45Types-elem (vector (lambda (u--acc) (lambda (u--elem) (lambda (u--func) (lambda (u--init) (lambda (u--input) (PreludeC-45Types-u--foldr_Foldable_List u--func u--init u--input)))))) (lambda (u--elem) (lambda (u--acc) (lambda (u--func) (lambda (u--init) (lambda (u--input) (PreludeC-45Types-u--foldl_Foldable_List u--func u--init u--input)))))) (lambda (u--elem) (lambda (arg-10980) (PreludeC-45Types-u--null_Foldable_List arg-10980))) (lambda (u--elem) (lambda (u--acc) (lambda (u--m) (lambda (i_con-0) (lambda (u--funcM) (lambda (u--init) (lambda (u--input) (PreludeC-45Types-u--foldlM_Foldable_List i_con-0 u--funcM u--init u--input)))))))) (lambda (u--elem) (lambda (arg-11009) arg-11009)) (lambda (u--a) (lambda (u--m) (lambda (i_con-0) (lambda (u--f) (lambda (arg-11023) (PreludeC-45Types-u--foldMap_Foldable_List i_con-0 u--f arg-11023))))))) (cons (lambda (arg-712) (lambda (arg-715) (PreludeC-45EqOrd-u--C-61C-61_Eq_String arg-712 arg-715))) (lambda (arg-722) (lambda (arg-725) (PreludeC-45EqOrd-u--C-47C-61_Eq_String arg-722 arg-725)))) SystemC-45Info-os (cons "windows" (cons "mingw32" (cons "cygwin32" '())))))
(define SystemC-45FileC-45Mode-modeStr (lambda (arg-0) (cond ((equal? arg-0 0) (let ((sc1 SystemC-45Info-isWindows)) (cond ((equal? sc1 1) "rb") (else "r")))) ((equal? arg-0 1) (let ((sc1 SystemC-45Info-isWindows)) (cond ((equal? sc1 1) "wb") (else "w")))) ((equal? arg-0 2) (let ((sc1 SystemC-45Info-isWindows)) (cond ((equal? sc1 1) "ab") (else "a")))) ((equal? arg-0 3) (let ((sc1 SystemC-45Info-isWindows)) (cond ((equal? sc1 1) "rb+") (else "r+")))) ((equal? arg-0 4) (let ((sc1 SystemC-45Info-isWindows)) (cond ((equal? sc1 1) "wb+") (else "w+")))) (else (let ((sc1 SystemC-45Info-isWindows)) (cond ((equal? sc1 1) "ab+") (else "a+")))))))
(define PreludeC-45EqOrd-u--C-47C-61_Eq_Int (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-61C-61_Eq_Int arg-0 arg-1))) (cond ((equal? sc0 1) 0) (else 1)))))
(define SystemC-45FileC-45Handle-openFile (lambda (arg-1 arg-2 arg-3) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-1))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45Handle-prim__open arg-2 (SystemC-45FileC-45Mode-modeStr arg-3) eta-0))))) (lambda (u--res) (let ((sc1 (PreludeC-45EqOrd-u--C-47C-61_Eq_Int (PrimIO-prim__nullAnyPtr u--res) (blodwen-toSignedInt 0 63)))) (cond ((equal? sc1 1) (SystemC-45FileC-45Error-returnError arg-1)) (else (SystemC-45FileC-45Support-ok arg-1 u--res))))))))))
(define SystemC-45FileC-45Handle-withFile (lambda (arg-3 arg-4 arg-5 arg-6 arg-7) (let ((e-1 (car arg-3))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (SystemC-45FileC-45Handle-openFile arg-3 arg-4 arg-5)) (lambda (_-0) (case (vector-ref _-0 0) ((1) (let ((e-6 (vector-ref _-0 1))) (let ((e-8 (car arg-3))) (let ((e-10 (vector-ref e-8 1))) ((((e-10 'erased) 'erased) (arg-7 e-6)) (lambda (u--res) (let ((e-13 (car arg-3))) (let ((e-15 (vector-ref e-13 1))) ((((e-15 'erased) 'erased) (SystemC-45FileC-45Handle-closeFile arg-3 e-6)) (lambda (_-10718) (let ((e-18 (car arg-3))) (let ((e-21 (vector-ref e-18 0))) (let ((e-23 (vector-ref e-21 1))) ((e-23 'erased) u--res)))))))))))))) (else (let ((e-6 (vector-ref _-0 1))) (let ((e-8 (car arg-3))) (let ((e-11 (vector-ref e-8 0))) (let ((e-14 (vector-ref e-11 0))) ((((e-14 'erased) 'erased) (lambda (eta-0) (vector 0 eta-0))) (arg-6 e-6))))))))))))))
(define SystemC-45FileC-45ReadWrite-writeFile (lambda (arg-1 arg-2 arg-3) (SystemC-45FileC-45Handle-withFile arg-1 arg-2 1 (lambda (eta-0) (let ((e-1 (car arg-1))) (let ((e-5 (vector-ref e-1 0))) (let ((e-7 (vector-ref e-5 1))) ((e-7 'erased) eta-0))))) (lambda (eta-0) (PreludeC-45Basics-flip (lambda (eta-1) (lambda (eta-2) (SystemC-45FileC-45ReadWrite-fPutStr arg-1 eta-1 eta-2))) arg-3 eta-0)))))
(define PreludeC-45Show-firstCharIs (lambda (arg-0 arg-1) (cond ((equal? arg-1 "") 0)(else (arg-0 (string-ref arg-1 0))))))
(define PreludeC-45Show-showParens (lambda (arg-0 arg-1) (cond ((equal? arg-0 0) arg-1) (else (string-append "(" (string-append arg-1 ")"))))))
(define PreludeC-45EqOrd-u--C-61C-61_Eq_Char (lambda (arg-0 arg-1) (let ((sc0 (or (and (char=? arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45EqOrd-u--C-61C-61_Eq_Ordering (lambda (arg-0 arg-1) (cond ((equal? arg-0 0) (cond ((equal? arg-1 0) 1)(else 0))) ((equal? arg-0 1) (cond ((equal? arg-1 1) 1)(else 0))) ((equal? arg-0 2) (cond ((equal? arg-1 2) 1)(else 0)))(else 0))))
(define PreludeC-45EqOrd-u--C-47C-61_Eq_Ordering (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-61C-61_Eq_Ordering arg-0 arg-1))) (cond ((equal? sc0 1) 0) (else 1)))))
(define PreludeC-45Show-precCon (lambda (arg-0) (case (vector-ref arg-0 0) ((0) 0) ((1) 1) ((2) 2) ((3) 3) ((4) 4) ((5) 5) (else 6))))
(define PreludeC-45EqOrd-u--C-60_Ord_Integer (lambda (arg-0 arg-1) (let ((sc0 (or (and (< arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45EqOrd-u--C-61C-61_Eq_Integer (lambda (arg-0 arg-1) (let ((sc0 (or (and (= arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45EqOrd-u--compare_Ord_Integer (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-60_Ord_Integer arg-0 arg-1))) (cond ((equal? sc0 1) 0) (else (let ((sc1 (PreludeC-45EqOrd-u--C-61C-61_Eq_Integer arg-0 arg-1))) (cond ((equal? sc1 1) 1) (else 2))))))))
(define PreludeC-45Show-u--compare_Ord_Prec (lambda (arg-0 arg-1) (case (vector-ref arg-0 0) ((4) (let ((e-0 (vector-ref arg-0 1))) (case (vector-ref arg-1 0) ((4) (let ((e-1 (vector-ref arg-1 1))) (PreludeC-45EqOrd-u--compare_Ord_Integer e-0 e-1)))(else (PreludeC-45EqOrd-u--compare_Ord_Integer (PreludeC-45Show-precCon arg-0) (PreludeC-45Show-precCon arg-1))))))(else (PreludeC-45EqOrd-u--compare_Ord_Integer (PreludeC-45Show-precCon arg-0) (PreludeC-45Show-precCon arg-1))))))
(define PreludeC-45Show-u--C-62C-61_Ord_Prec (lambda (arg-0 arg-1) (PreludeC-45EqOrd-u--C-47C-61_Eq_Ordering (PreludeC-45Show-u--compare_Ord_Prec arg-0 arg-1) 0)))
(define PreludeC-45Show-primNumShow (lambda (arg-1 arg-2 arg-3) (let ((u--str (arg-1 arg-3))) (PreludeC-45Show-showParens (let ((sc0 (PreludeC-45Show-u--C-62C-61_Ord_Prec arg-2 (vector 5 )))) (cond ((equal? sc0 1) (PreludeC-45Show-firstCharIs (lambda (arg-0) (PreludeC-45EqOrd-u--C-61C-61_Eq_Char arg-0 #\-)) u--str)) (else 0))) u--str))))
(define PreludeC-45Show-u--showPrec_Show_Double (lambda (ext-0 ext-1) (PreludeC-45Show-primNumShow (lambda (eta-0) (number->string eta-0)) ext-0 ext-1)))
(define PreludeC-45Show-u--show_Show_Double (lambda (arg-0) (PreludeC-45Show-u--showPrec_Show_Double (vector 0 ) arg-0)))
(define PreludeC-45Show-u--showPrec_Show_Integer (lambda (ext-0 ext-1) (PreludeC-45Show-primNumShow (lambda (eta-0) (number->string eta-0)) ext-0 ext-1)))
(define PreludeC-45Show-u--show_Show_Integer (lambda (arg-0) (PreludeC-45Show-u--showPrec_Show_Integer (vector 0 ) arg-0)))
(define PreludeC-45Show-u--show_Show_Nat (lambda (arg-0) (PreludeC-45Show-u--show_Show_Integer arg-0)))
(define LinearRegression-case--caseC-32blockC-32inC-32runLinearRegressionExperiment-8805 (lambda (arg-0 arg-1 arg-2 arg-3 arg-4 arg-5) (let ((e-2 (car arg-5))) (let ((e-3 (cdr arg-5))) (lambda () (lambda (world-0) (let ((act-1 (PreludeC-45IO-prim__putStr "\xa; Training Results:\xa;" world-0))) (let ((act-2 (PreludeC-45IO-prim__putStr (string-append (string-append "   MSE: " (PreludeC-45Show-u--show_Show_Double e-2)) "\xa;") world-0))) (let ((act-3 (PreludeC-45IO-prim__putStr (string-append (string-append "   R\xb2;:  " (PreludeC-45Show-u--show_Show_Double e-3)) "\xa;") world-0))) (let ((act-4 (PreludeC-45IO-prim__putStr "\xa; Saving results for comparison...\xa;" world-0))) (((let ((e-5 (car arg-4))) (let ((e-4 (cdr arg-4))) (let ((u--predictions (LinearRegression-predictBatch 1000 arg-0 arg-4))) (lambda () (lambda (world-1) (let ((act-5 ((System-system (force csegen-13) "mkdir -p results") world-1))) (let ((u--w0 (PreludeC-45Show-u--show_Show_Double (DataC-45Vect-index 0 e-5)))) (let ((u--w1 (PreludeC-45Show-u--show_Show_Double (DataC-45Vect-index 1 e-5)))) (let ((u--w2 (PreludeC-45Show-u--show_Show_Double (DataC-45Vect-index 2 e-5)))) (let ((u--w3 (PreludeC-45Show-u--show_Show_Double (DataC-45Vect-index 3 e-5)))) (let ((u--w4 (PreludeC-45Show-u--show_Show_Double (DataC-45Vect-index 4 e-5)))) (let ((u--w5 (PreludeC-45Show-u--show_Show_Double (DataC-45Vect-index 5 e-5)))) (let ((u--w6 (PreludeC-45Show-u--show_Show_Double (DataC-45Vect-index 6 e-5)))) (let ((u--w7 (PreludeC-45Show-u--show_Show_Double (DataC-45Vect-index 7 e-5)))) (let ((u--weightsStr (string-append u--w0 (string-append "\xa;" (string-append u--w1 (string-append "\xa;" (string-append u--w2 (string-append "\xa;" (string-append u--w3 (string-append "\xa;" (string-append u--w4 (string-append "\xa;" (string-append u--w5 (string-append "\xa;" (string-append u--w6 (string-append "\xa;" u--w7)))))))))))))))) (let ((act-6 ((SystemC-45FileC-45ReadWrite-writeFile (force csegen-13) "results/idris_linear_weights.txt" u--weightsStr) world-1))) (case (vector-ref act-6 0) ((1) (let ((act-7 ((SystemC-45FileC-45ReadWrite-writeFile (force csegen-13) "results/idris_linear_bias.txt" (PreludeC-45Show-u--show_Show_Double e-4)) world-1))) (case (vector-ref act-7 0) ((1) (let ((u--p0 (PreludeC-45Show-u--show_Show_Double (DataC-45Vect-index 0 u--predictions)))) (let ((u--p1 (PreludeC-45Show-u--show_Show_Double (DataC-45Vect-index 1 u--predictions)))) (let ((u--p2 (PreludeC-45Show-u--show_Show_Double (DataC-45Vect-index 2 u--predictions)))) (let ((u--p3 (PreludeC-45Show-u--show_Show_Double (DataC-45Vect-index 3 u--predictions)))) (let ((u--p4 (PreludeC-45Show-u--show_Show_Double (DataC-45Vect-index 4 u--predictions)))) (let ((u--predStr (string-append u--p0 (string-append "\xa;" (string-append u--p1 (string-append "\xa;" (string-append u--p2 (string-append "\xa;" (string-append u--p3 (string-append "\xa;" u--p4)))))))))) (let ((act-8 ((SystemC-45FileC-45ReadWrite-writeFile (force csegen-13) "results/idris_linear_predictions.txt" u--predStr) world-1))) (case (vector-ref act-8 0) ((1) (let ((act-9 (PreludeC-45IO-prim__putStr "\xa; Shape Safety Verification:\xa;" world-1))) (let ((act-10 (PreludeC-45IO-prim__putStr (string-append (string-append "    Input shape: [5, " (string-append (PreludeC-45Show-u--show_Show_Nat LinearRegression-FEATURES) "] - GUARANTEED")) "\xa;") world-1))) (let ((act-11 (PreludeC-45IO-prim__putStr (string-append (string-append "    Weight shape: [" (string-append (PreludeC-45Show-u--show_Show_Nat LinearRegression-FEATURES) "] - GUARANTEED")) "\xa;") world-1))) (let ((act-12 (PreludeC-45IO-prim__putStr "    Output shape: [5] - GUARANTEED\xa;" world-1))) (let ((act-13 (PreludeC-45IO-prim__putStr "    Matrix operations: COMPILE-TIME verified\xa;" world-1))) (let ((act-14 (PreludeC-45IO-prim__putStr "    No runtime shape errors possible!\xa;" world-1))) (let ((act-15 (PreludeC-45IO-prim__putStr "\xa; Impossible at compile time:\xa;" world-1))) (let ((act-16 (PreludeC-45IO-prim__putStr "   \x2022; Input with 7 features -> COMPILE ERROR\xa;" world-1))) (let ((act-17 (PreludeC-45IO-prim__putStr "   \x2022; Input with 9 features -> COMPILE ERROR\xa;" world-1))) (let ((act-18 (PreludeC-45IO-prim__putStr "   \x2022; Wrong weight dimensions -> COMPILE ERROR\xa;" world-1))) (let ((act-19 (PreludeC-45IO-prim__putStr "\xa; Fair comparison linear regression completed!\xa;" world-1))) (PreludeC-45IO-prim__putStr "   Same algorithm, hyperparameters, and evaluation as Python\xa;" world-1))))))))))))) (else (PreludeC-45IO-prim__putStr "\x26a0;\xfe0f;  Warning: Could not save predictions\xa;" world-1))))))))))) (else (PreludeC-45IO-prim__putStr "\x26a0;\xfe0f;  Warning: Could not save bias\xa;" world-1))))) (else (PreludeC-45IO-prim__putStr "\x26a0;\xfe0f;  Warning: Could not save weights\xa;" world-1)))))))))))))))))))) world-0)))))))))))
(define LinearRegression-LEARNING_RATE 0.01)
(define LinearRegression-computeBiasGradient (lambda (arg-0 arg-1) (/ (PreludeC-45Interfaces-sum csegen-20 csegen-36 arg-1) (exact->inexact arg-0))))
(define LinearRegression-computeWeightGradients (lambda (arg-0 arg-1 arg-2) (let ((u--grad0 (/ (PreludeC-45Interfaces-sum csegen-20 csegen-36 (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 csegen-38 (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (u--row) (DataC-45Vect-index 0 u--row)) arg-1) arg-2)) (exact->inexact arg-0)))) (let ((u--grad1 (/ (PreludeC-45Interfaces-sum csegen-20 csegen-36 (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 csegen-38 (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (u--row) (DataC-45Vect-index 1 u--row)) arg-1) arg-2)) (exact->inexact arg-0)))) (let ((u--grad2 (/ (PreludeC-45Interfaces-sum csegen-20 csegen-36 (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 csegen-38 (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (u--row) (DataC-45Vect-index 2 u--row)) arg-1) arg-2)) (exact->inexact arg-0)))) (let ((u--grad3 (/ (PreludeC-45Interfaces-sum csegen-20 csegen-36 (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 csegen-38 (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (u--row) (DataC-45Vect-index 3 u--row)) arg-1) arg-2)) (exact->inexact arg-0)))) (let ((u--grad4 (/ (PreludeC-45Interfaces-sum csegen-20 csegen-36 (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 csegen-38 (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (u--row) (DataC-45Vect-index 4 u--row)) arg-1) arg-2)) (exact->inexact arg-0)))) (let ((u--grad5 (/ (PreludeC-45Interfaces-sum csegen-20 csegen-36 (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 csegen-38 (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (u--row) (DataC-45Vect-index 5 u--row)) arg-1) arg-2)) (exact->inexact arg-0)))) (let ((u--grad6 (/ (PreludeC-45Interfaces-sum csegen-20 csegen-36 (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 csegen-38 (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (u--row) (DataC-45Vect-index 6 u--row)) arg-1) arg-2)) (exact->inexact arg-0)))) (let ((u--grad7 (/ (PreludeC-45Interfaces-sum csegen-20 csegen-36 (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 csegen-38 (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (u--row) (DataC-45Vect-index 7 u--row)) arg-1) arg-2)) (exact->inexact arg-0)))) (cons u--grad0 (cons u--grad1 (cons u--grad2 (cons u--grad3 (cons u--grad4 (cons u--grad5 (cons u--grad6 (cons u--grad7 '()))))))))))))))))))
(define LinearRegression-trainStep (lambda (arg-0 arg-1 arg-2 arg-3) (let ((e-2 (car arg-3))) (let ((e-3 (cdr arg-3))) (let ((u--predictions (LinearRegression-predictBatch arg-0 arg-1 (cons e-2 e-3)))) (let ((u--errors (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 csegen-15 u--predictions arg-2))) (let ((u--weightGradients (LinearRegression-computeWeightGradients arg-0 arg-1 u--errors))) (let ((u--biasGradient (LinearRegression-computeBiasGradient arg-0 u--errors))) (let ((u--newWeights (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 (lambda (u--w) (lambda (u--g) (- u--w (* LinearRegression-LEARNING_RATE u--g)))) e-2 u--weightGradients))) (let ((u--newBias (- e-3 (* LinearRegression-LEARNING_RATE u--biasGradient)))) (cons u--newWeights u--newBias)))))))))))
(define LinearRegression-trainModel (lambda (arg-0 arg-1 arg-2 arg-3 arg-4) (cond ((equal? arg-4 0) arg-3)(else (let ((e-0 (- arg-4 1))) (let ((u--newModel (LinearRegression-trainStep arg-0 arg-1 arg-2 arg-3))) (LinearRegression-trainModel arg-0 arg-1 arg-2 u--newModel e-0)))))))
(define LinearRegression-MAX_ITERATIONS 1000)
(define LinearRegression-REGULARIZATION 0.001)
(define LinearRegression-SAMPLE_SIZE 1000)
(define LinearRegression-mseLoss (lambda (arg-0 arg-1 arg-2) (let ((u--errors (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 csegen-15 arg-1 arg-2))) (let ((u--squaredErrors (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (u--e) (* u--e u--e)) u--errors))) (let ((u--sumSquares (PreludeC-45Interfaces-sum csegen-20 csegen-36 u--squaredErrors))) (/ u--sumSquares (exact->inexact arg-0)))))))
(define PreludeC-45EqOrd-u--C-62_Ord_Double (lambda (arg-0 arg-1) (let ((sc0 (or (and (> arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define DataC-45Vect-u--zip_Zippable_C-40VectC-32C-36kC-41 (lambda (ext-0 ext-1) (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 (lambda (__leftTupleSection-0) (lambda (__infixTupleSection-0) (cons __leftTupleSection-0 __infixTupleSection-0))) ext-0 ext-1)))
(define LinearRegression-rSquared (lambda (arg-0 arg-1 arg-2) (let ((u--actualMean (/ (PreludeC-45Interfaces-sum csegen-20 csegen-36 arg-2) (exact->inexact arg-0)))) (let ((u--totalSumSquares (PreludeC-45Interfaces-sum csegen-20 csegen-36 (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (u--y) (* (- u--y u--actualMean) (- u--y u--actualMean))) arg-2)))) (let ((u--residualSumSquares (PreludeC-45Interfaces-sum csegen-20 csegen-36 (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (* (- e-2 e-3) (- e-2 e-3))))) (DataC-45Vect-u--zip_Zippable_C-40VectC-32C-36kC-41 arg-1 arg-2))))) (let ((sc0 (PreludeC-45EqOrd-u--C-62_Ord_Double u--totalSumSquares 0.0))) (cond ((equal? sc0 1) (- 1.0 (/ u--residualSumSquares u--totalSumSquares))) (else 0.0))))))))
(define LinearRegression-evaluateModel (lambda (arg-0 arg-1 arg-2 arg-3) (let ((u--predictions (LinearRegression-predictBatch arg-0 arg-1 arg-3))) (let ((u--mse (LinearRegression-mseLoss arg-0 u--predictions arg-2))) (let ((u--r2 (LinearRegression-rSquared arg-0 u--predictions arg-2))) (cons u--mse u--r2))))))
(define System-u--cast_Cast_ExitCode_Int (lambda (arg-0) (if (null? arg-0) 0 (let ((e-0 (car arg-0))) e-0))))
(define System-exitWith (lambda (arg-2 ext-0) (let ((e-2 (cdr arg-2))) ((e-2 'erased) (lambda (eta-1) (System-prim__exit (System-u--cast_Cast_ExitCode_Int ext-0) eta-1))))))
(define LinearRegression-initializeModel (let ((u--weights (cons 0.005 (cons (- 0.008) (cons 0.012 (cons (- 0.003) (cons 0.009 (cons (- 0.006) (cons 0.011 (cons (- 0.004) '())))))))))) (cons u--weights 0.1)))
(define PreludeC-45TypesC-45List-lengthPlus (lambda (arg-1 arg-2) (if (null? arg-2) arg-1 (let ((e-3 (cdr arg-2))) (PreludeC-45TypesC-45List-lengthPlus (+ arg-1 1) e-3)))))
(define PreludeC-45TypesC-45List-lengthTR (lambda (ext-0) (PreludeC-45TypesC-45List-lengthPlus 0 ext-0)))
(define PreludeC-45TypesC-45List-reverseOnto (lambda (arg-1 arg-2) (if (null? arg-2) arg-1 (let ((e-2 (car arg-2))) (let ((e-3 (cdr arg-2))) (PreludeC-45TypesC-45List-reverseOnto (cons e-2 arg-1) e-3))))))
(define PreludeC-45TypesC-45List-reverse (lambda (ext-0) (PreludeC-45TypesC-45List-reverseOnto '() ext-0)))
(define DataC-45String-n--4012-9579-u--linesHelp (lambda (arg-0 arg-1 arg-2) (if (null? arg-1) (if (null? arg-2) '() (if (null? arg-2) (cons (PreludeC-45TypesC-45List-reverse arg-1) '()) (let ((e-2 (car arg-2))) (let ((e-3 (cdr arg-2))) (cond ((equal? e-2 (integer->char 10)) (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--4012-9579-u--linesHelp arg-0 '() e-3))) ((equal? e-2 (integer->char 13)) (if (null? e-3) (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--4012-9579-u--linesHelp arg-0 '() e-3)) (let ((e-5 (car e-3))) (let ((e-6 (cdr e-3))) (cond ((equal? e-5 (integer->char 10)) (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--4012-9579-u--linesHelp arg-0 '() e-6)))(else (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--4012-9579-u--linesHelp arg-0 '() e-3))))))))(else (DataC-45String-n--4012-9579-u--linesHelp arg-0 (cons e-2 arg-1) e-3))))))) (if (null? arg-2) (cons (PreludeC-45TypesC-45List-reverse arg-1) '()) (let ((e-2 (car arg-2))) (let ((e-3 (cdr arg-2))) (cond ((equal? e-2 (integer->char 10)) (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--4012-9579-u--linesHelp arg-0 '() e-3))) ((equal? e-2 (integer->char 13)) (if (null? e-3) (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--4012-9579-u--linesHelp arg-0 '() e-3)) (let ((e-5 (car e-3))) (let ((e-6 (cdr e-3))) (cond ((equal? e-5 (integer->char 10)) (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--4012-9579-u--linesHelp arg-0 '() e-6)))(else (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--4012-9579-u--linesHelp arg-0 '() e-3))))))))(else (DataC-45String-n--4012-9579-u--linesHelp arg-0 (cons e-2 arg-1) e-3)))))))))
(define DataC-45String-linesC-39 (lambda (arg-0) (DataC-45String-n--4012-9579-u--linesHelp arg-0 '() arg-0)))
(define PreludeC-45TypesC-45SnocList-C-60C-62C-62 (lambda (arg-1 arg-2) (if (null? arg-1) arg-2 (let ((e-2 (car arg-1))) (let ((e-3 (cdr arg-1))) (PreludeC-45TypesC-45SnocList-C-60C-62C-62 e-2 (cons e-3 arg-2)))))))
(define PreludeC-45TypesC-45List-mapAppend (lambda (arg-2 arg-3 arg-4) (if (null? arg-4) (PreludeC-45TypesC-45SnocList-C-60C-62C-62 arg-2 '()) (let ((e-1 (car arg-4))) (let ((e-2 (cdr arg-4))) (PreludeC-45TypesC-45List-mapAppend (cons arg-2 (arg-3 e-1)) arg-3 e-2))))))
(define DataC-45String-lines (lambda (arg-0) (PreludeC-45TypesC-45List-mapAppend '() (lambda (eta-0) (PreludeC-45Types-fastPack eta-0)) (DataC-45String-linesC-39 (PreludeC-45Types-fastUnpack arg-0)))))
(define LinearRegression-exactLength (lambda (arg-1 arg-2) (cond ((equal? arg-1 0) (if (null? arg-2) (box '()) '()))(else (let ((e-0 (- arg-1 1))) (if (null? arg-2) '() (let ((e-7 (car arg-2))) (let ((e-8 (cdr arg-2))) (PreludeC-45Types-u--map_Functor_Maybe (lambda (arg-0) (cons e-7 arg-0)) (LinearRegression-exactLength e-0 e-8))))))))))
(define DataC-45List-drop (lambda (arg-1 arg-2) (cond ((equal? arg-1 0) arg-2)(else (let ((e-0 (- arg-1 1))) (if (null? arg-2) '() (let ((e-4 (cdr arg-2))) (DataC-45List-drop e-0 e-4))))))))
(define LinearRegression-listToVect (lambda (arg-1 arg-2) (cond ((equal? arg-1 0) (if (null? arg-2) (box '()) '()))(else (let ((e-0 (- arg-1 1))) (if (null? arg-2) '() (let ((e-7 (car arg-2))) (let ((e-8 (cdr arg-2))) (PreludeC-45Types-u--map_Functor_Maybe (lambda (arg-0) (cons e-7 arg-0)) (LinearRegression-listToVect e-0 e-8))))))))))
(define DataC-45String-strM (lambda (arg-0) (cond ((equal? arg-0 "") '())(else (cons (string-ref arg-0 0) (substring arg-0 1 (string-length arg-0)))))))
(define DataC-45String-with--asList-9717 (lambda (arg-0 arg-1) (cond ((equal? arg-0 "") (if (null? arg-1) (vector 0 ) (let ((e-0 (car arg-1))) (let ((e-1 (cdr arg-1))) (vector 1 e-0 e-1 (lambda () (DataC-45String-asList e-1)))))))(else (let ((e-0 (car arg-1))) (let ((e-1 (cdr arg-1))) (vector 1 e-0 e-1 (lambda () (DataC-45String-asList e-1)))))))))
(define DataC-45String-asList (lambda (arg-0) (DataC-45String-with--asList-9717 arg-0 (DataC-45String-strM arg-0))))
(define PreludeC-45Types-isSpace (lambda (arg-0) (cond ((equal? arg-0 #\ ) 1) ((equal? arg-0 (integer->char 9)) 1) ((equal? arg-0 (integer->char 13)) 1) ((equal? arg-0 (integer->char 10)) 1) ((equal? arg-0 (integer->char 12)) 1) ((equal? arg-0 (integer->char 11)) 1) ((equal? arg-0 (integer->char 160)) 1)(else 0))))
(define DataC-45String-with--ltrim-9741 (lambda (arg-0 arg-1) (cond ((equal? arg-0 "") (case (vector-ref arg-1 0) ((0) "")(else (let ((e-0 (vector-ref arg-1 1))) (let ((e-1 (vector-ref arg-1 2))) (let ((e-2 (vector-ref arg-1 3))) (let ((u--str (string-cons e-0 e-1))) (let ((sc2 (PreludeC-45Types-isSpace e-0))) (cond ((equal? sc2 1) (DataC-45String-with--ltrim-9741 e-1 (e-2))) (else u--str))))))))))(else (let ((e-0 (vector-ref arg-1 1))) (let ((e-1 (vector-ref arg-1 2))) (let ((e-2 (vector-ref arg-1 3))) (let ((u--str (string-cons e-0 e-1))) (let ((sc1 (PreludeC-45Types-isSpace e-0))) (cond ((equal? sc1 1) (DataC-45String-with--ltrim-9741 e-1 (e-2))) (else u--str)))))))))))
(define DataC-45String-ltrim (lambda (arg-0) (DataC-45String-with--ltrim-9741 arg-0 (DataC-45String-asList arg-0))))
(define DataC-45String-trim (lambda (ext-0) (DataC-45String-ltrim (string-reverse (DataC-45String-ltrim (string-reverse ext-0))))))
(define LinearRegression-parseCSVDouble (lambda (arg-0) (let ((u--trimmed (DataC-45String-trim arg-0))) (let ((sc0 (PreludeC-45EqOrd-u--C-61C-61_Eq_String u--trimmed ""))) (cond ((equal? sc0 1) '()) (else (box (cast-string-double u--trimmed))))))))
(define DataC-45List-span (lambda (arg-1 arg-2) (if (null? arg-2) (cons '() '()) (let ((e-2 (car arg-2))) (let ((e-3 (cdr arg-2))) (let ((sc1 (arg-1 e-2))) (cond ((equal? sc1 1) (let ((sc2 (DataC-45List-span arg-1 e-3))) (let ((e-5 (car sc2))) (let ((e-4 (cdr sc2))) (cons (cons e-2 e-5) e-4))))) (else (cons '() (cons e-2 e-3))))))))))
(define DataC-45List-break (lambda (arg-1 arg-2) (DataC-45List-span (lambda (eta-0) (let ((sc0 (arg-1 eta-0))) (cond ((equal? sc0 1) 0) (else 1)))) arg-2)))
(define DataC-45List1-singleton (lambda (arg-1) (cons arg-1 '())))
(define DataC-45List-split (lambda (arg-1 arg-2) (let ((sc0 (DataC-45List-break arg-1 arg-2))) (let ((e-2 (car sc0))) (let ((e-3 (cdr sc0))) (if (null? e-3) (DataC-45List1-singleton e-2) (let ((e-7 (cdr e-3))) (cons e-2 (DataC-45List-split arg-1 e-7)))))))))
(define DataC-45List1-u--map_Functor_List1 (lambda (arg-2 arg-3) (let ((e-1 (car arg-3))) (let ((e-2 (cdr arg-3))) (cons (arg-2 e-1) (PreludeC-45TypesC-45List-mapAppend '() arg-2 e-2))))))
(define DataC-45String-split (lambda (arg-0 arg-1) (DataC-45List1-u--map_Functor_List1 (lambda (eta-0) (PreludeC-45Types-fastPack eta-0)) (DataC-45List-split arg-0 (PreludeC-45Types-fastUnpack arg-1)))))
(define DataC-45List-take (lambda (arg-1 arg-2) (cond ((equal? arg-1 0) '())(else (let ((e-0 (- arg-1 1))) (if (null? arg-2) '() (let ((e-2 (car arg-2))) (let ((e-3 (cdr arg-2))) (cons e-2 (DataC-45List-take e-0 e-3))))))))))
(define PreludeC-45Types-u--C-62C-62C-61_Monad_Maybe (lambda (arg-2 arg-3) (if (null? arg-2) '() (let ((e-2 (unbox arg-2))) (arg-3 e-2)))))
(define PreludeC-45Types-u--traverse_Traversable_List (lambda (arg-3 arg-4 arg-5) (if (null? arg-5) (let ((e-2 (vector-ref arg-3 1))) ((e-2 'erased) '())) (let ((e-2 (car arg-5))) (let ((e-3 (cdr arg-5))) (let ((e-4 (vector-ref arg-3 2))) ((((e-4 'erased) 'erased) (let ((e-6 (vector-ref arg-3 2))) ((((e-6 'erased) 'erased) (let ((e-10 (vector-ref arg-3 1))) ((e-10 'erased) csegen-66))) (arg-4 e-2)))) (PreludeC-45Types-u--traverse_Traversable_List arg-3 arg-4 e-3))))))))
(define LinearRegression-parseCSVLine (lambda (arg-0) (let ((u--fieldsList1 (DataC-45String-split (lambda (arg-1) (PreludeC-45EqOrd-u--C-61C-61_Eq_Char arg-1 #\,)) arg-0))) (let ((sc0 (PreludeC-45TypesC-45List-lengthTR u--fieldsList1))) (cond ((equal? sc0 0) '())(else (let ((e-0 (- (PreludeC-45TypesC-45List-lengthTR u--fieldsList1) 1))) (cond ((equal? e-0 0) '())(else (let ((e-1 (- e-0 1))) (cond ((equal? e-1 0) '())(else (let ((e-2 (- e-1 1))) (cond ((equal? e-2 0) '())(else (let ((e-3 (- e-2 1))) (cond ((equal? e-3 0) '())(else (let ((e-4 (- e-3 1))) (cond ((equal? e-4 0) '())(else (let ((e-5 (- e-4 1))) (cond ((equal? e-5 0) '())(else (let ((e-6 (- e-5 1))) (cond ((equal? e-6 0) '())(else (let ((e-7 (- e-6 1))) (cond ((equal? e-7 0) '())(else (let ((e-8 (- e-7 1))) (cond ((equal? e-8 0) (let ((u--featureFields (DataC-45List-take 8 u--fieldsList1))) (let ((u--targetField (let ((sc1 (DataC-45List-drop 8 u--fieldsList1))) (if (null? sc1) "" (let ((e-10 (car sc1))) e-10))))) (PreludeC-45Types-u--C-62C-62C-61_Monad_Maybe (PreludeC-45Types-u--traverse_Traversable_List csegen-46 (lambda (eta-0) (LinearRegression-parseCSVDouble eta-0)) u--featureFields) (lambda (u--features) (PreludeC-45Types-u--C-62C-62C-61_Monad_Maybe (LinearRegression-parseCSVDouble u--targetField) (lambda (u--target) (PreludeC-45Types-u--C-62C-62C-61_Monad_Maybe (LinearRegression-listToVect LinearRegression-FEATURES u--features) (lambda (u--featuresVect) (box (cons u--featuresVect u--target)))))))))))(else '())))))))))))))))))))))))))))))))))
(define DataC-45List-u--unzipWith_Zippable_List (lambda (arg-3 arg-4) (if (null? arg-4) (cons '() '()) (let ((e-2 (car arg-4))) (let ((e-3 (cdr arg-4))) (let ((sc1 (arg-3 e-2))) (let ((e-5 (car sc1))) (let ((e-4 (cdr sc1))) (let ((sc2 (DataC-45List-u--unzipWith_Zippable_List arg-3 e-3))) (let ((e-7 (car sc2))) (let ((e-6 (cdr sc2))) (cons (cons e-5 e-7) (cons e-4 e-6)))))))))))))
(define DataC-45List-u--unzip_Zippable_List (lambda (ext-0) (DataC-45List-u--unzipWith_Zippable_List (lambda (eta-0) eta-0) ext-0)))
(define LinearRegression-parseAllLines (lambda (arg-0) (PreludeC-45Types-u--C-62C-62C-61_Monad_Maybe (PreludeC-45Types-u--traverse_Traversable_List csegen-46 (lambda (eta-0) (LinearRegression-parseCSVLine eta-0)) arg-0) (lambda (u--parsedData) (let ((sc0 (DataC-45List-u--unzip_Zippable_List u--parsedData))) (let ((e-2 (car sc0))) (let ((e-3 (cdr sc0))) (PreludeC-45Types-u--C-62C-62C-61_Monad_Maybe (LinearRegression-exactLength LinearRegression-SAMPLE_SIZE e-2) (lambda (u--featuresVect) (PreludeC-45Types-u--C-62C-62C-61_Monad_Maybe (LinearRegression-exactLength LinearRegression-SAMPLE_SIZE e-3) (lambda (u--targetsVect) (box (cons u--featuresVect u--targetsVect)))))))))))))
(define DataC-45Fuel-forever (lambda () (box (lambda () (DataC-45Fuel-forever)))))
(define PreludeC-45Interfaces-C-42C-62 (lambda (arg-3 arg-4 arg-5) (let ((e-3 (vector-ref arg-3 2))) ((((e-3 'erased) 'erased) (((let ((eff-0 (let ((e-6 (vector-ref arg-3 0))) e-6))) (lambda (arg-0) (lambda (arg-1) ((((eff-0 'erased) 'erased) arg-0) arg-1)))) (lambda (eta-0) (lambda (eta-1) eta-1))) arg-4)) arg-5))))
(define SystemC-45FileC-45ReadWrite-fEOF (lambda (arg-1 arg-2) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-1))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45ReadWrite-prim__eof arg-2 eta-0))))) (lambda (u--res) (let ((e-7 (car arg-1))) (let ((e-10 (vector-ref e-7 0))) (let ((e-12 (vector-ref e-10 1))) ((e-12 'erased) (PreludeC-45EqOrd-u--C-47C-61_Eq_Int u--res (blodwen-toSignedInt 0 63))))))))))))
(define SystemC-45FileC-45Error-fileError (lambda (arg-1 arg-2) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-1))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45Error-prim__error arg-2 eta-0))))) (lambda (u--x) (let ((e-7 (car arg-1))) (let ((e-10 (vector-ref e-7 0))) (let ((e-12 (vector-ref e-10 1))) ((e-12 'erased) (PreludeC-45EqOrd-u--C-47C-61_Eq_Int u--x (blodwen-toSignedInt 0 63))))))))))))
(define SystemC-45FFI-free (lambda (arg-1 arg-2) (let ((e-2 (cdr arg-1))) ((e-2 'erased) (lambda (eta-0) (SystemC-45FFI-prim__free arg-2 eta-0))))))
(define PreludeC-45InterfacesC-45Applicative-u--pure_Applicative_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (lambda (arg-3 arg-4 ext-0) (let ((e-2 (vector-ref arg-3 1))) ((e-2 'erased) (let ((e-5 (vector-ref arg-4 1))) ((e-5 'erased) ext-0))))))
(define SystemC-45FileC-45ReadWrite-getStringAndFree (lambda (arg-1 arg-2 arg-3) (let ((sc0 (PreludeC-45EqOrd-u--C-47C-61_Eq_Int (PrimIO-prim__nullAnyPtr arg-3) (blodwen-toSignedInt 0 63)))) (cond ((equal? sc0 1) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (SystemC-45FileC-45Error-fileError arg-1 arg-2)) (lambda (bind-0) (cond ((equal? bind-0 1) (SystemC-45FileC-45Error-returnError arg-1)) (else (PreludeC-45InterfacesC-45Applicative-u--pure_Applicative_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (let ((e-7 (car arg-1))) (let ((e-10 (vector-ref e-7 0))) e-10)) (vector (lambda (u--b) (lambda (u--a) (lambda (u--func) (lambda (arg-8945) (case (vector-ref arg-8945 0) ((0) (let ((e-6 (vector-ref arg-8945 1))) (vector 0 e-6))) (else (let ((e-6 (vector-ref arg-8945 1))) (vector 1 (u--func e-6))))))))) (lambda (u--a) (lambda (arg-9991) (vector 1 arg-9991))) (lambda (u--b) (lambda (u--a) (lambda (arg-9997) (lambda (arg-10004) (case (vector-ref arg-9997 0) ((0) (let ((e-6 (vector-ref arg-9997 1))) (vector 0 e-6))) (else (let ((e-6 (vector-ref arg-9997 1))) (case (vector-ref arg-10004 0) ((1) (let ((e-8 (vector-ref arg-10004 1))) (vector 1 (e-6 e-8)))) (else (let ((e-11 (vector-ref arg-10004 1))) (vector 0 e-11)))))))))))) "")))))))) (else (let ((u--s (PreludeC-45IO-prim__getString arg-3))) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (SystemC-45FFI-free arg-1 arg-3)) (lambda (_-10718) (SystemC-45FileC-45Support-ok arg-1 u--s)))))))))))
(define SystemC-45FileC-45ReadWrite-fGetLine (lambda (arg-1 arg-2) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-1))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45ReadWrite-prim__readLine arg-2 eta-0))))) (lambda (u--res) (SystemC-45FileC-45ReadWrite-getStringAndFree arg-1 arg-2 u--res)))))))
(define SystemC-45FileC-45ReadWrite-fSeekLine (lambda (arg-1 arg-2) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-1))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45ReadWrite-prim__seekLine arg-2 eta-0))))) (lambda (u--res) (let ((sc1 (PreludeC-45EqOrd-u--C-47C-61_Eq_Int u--res (blodwen-toSignedInt 0 63)))) (cond ((equal? sc1 1) (SystemC-45FileC-45Error-returnError arg-1)) (else (SystemC-45FileC-45Support-ok arg-1 'erased))))))))))
(define PreludeC-45InterfacesC-45Applicative-u--C-60C-42C-62_Applicative_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (lambda (arg-4 arg-5 arg-6 arg-7) (let ((e-3 (vector-ref arg-4 2))) ((((e-3 'erased) 'erased) (let ((e-4 (vector-ref arg-4 2))) ((((e-4 'erased) 'erased) (let ((e-8 (vector-ref arg-4 1))) ((e-8 'erased) (lambda (clam-0) (lambda (clam-1) (let ((e-10 (vector-ref arg-5 2))) ((((e-10 'erased) 'erased) clam-0) clam-1))))))) arg-6))) arg-7))))
(define PreludeC-45Types-u--C-62C-62C-61_Monad_C-40EitherC-32C-36eC-41 (lambda (arg-3 arg-4) (case (vector-ref arg-3 0) ((0) (let ((e-2 (vector-ref arg-3 1))) (vector 0 e-2))) (else (let ((e-5 (vector-ref arg-3 1))) (arg-4 e-5))))))
(define PreludeC-45InterfacesC-45Monad-u--C-62C-62C-61_Monad_ComposeC-40C-40C-46C-32C-36mC-41C-32C-36tC-41 (lambda (arg-4 arg-5 arg-6 arg-7 arg-8) (let ((e-2 (vector-ref arg-4 1))) ((((e-2 'erased) 'erased) arg-7) (lambda (eta-0) (((let ((eff-0 (let ((e-6 (vector-ref arg-4 0))) (let ((e-9 (vector-ref e-6 0))) e-9)))) (lambda (arg-0) (lambda (arg-1) ((((eff-0 'erased) 'erased) arg-0) arg-1)))) (lambda (clam-0) (let ((e-4 (vector-ref arg-5 2))) ((e-4 'erased) clam-0)))) (let ((e-4 (vector-ref arg-6 2))) ((((((e-4 'erased) 'erased) 'erased) (let ((e-9 (vector-ref arg-4 0))) e-9)) arg-8) eta-0))))))))
(define PreludeC-45Types-u--foldr_Foldable_C-40EitherC-32C-36eC-41 (lambda (arg-3 arg-4 arg-5) (case (vector-ref arg-5 0) ((0) arg-4) (else (let ((e-5 (vector-ref arg-5 1))) ((arg-3 e-5) arg-4))))))
(define PreludeC-45Types-u--foldMap_Foldable_C-40EitherC-32C-36eC-41 (lambda (arg-3 arg-4 ext-0) (PreludeC-45Types-u--foldr_Foldable_C-40EitherC-32C-36eC-41 (lambda (eta-0) (lambda (eta-1) (let ((e-1 (car arg-3))) ((e-1 (arg-4 eta-0)) eta-1)))) (let ((e-2 (cdr arg-3))) e-2) ext-0)))
(define PreludeC-45Types-u--foldl_Foldable_C-40EitherC-32C-36eC-41 (lambda (arg-3 arg-4 arg-5) ((PreludeC-45Types-u--foldr_Foldable_C-40EitherC-32C-36eC-41 (lambda (eta-0) (lambda (eta-1) (PreludeC-45Basics-flip (lambda (eta-2) (lambda (eta-3) (lambda (eta-4) (eta-2 (eta-3 eta-4))))) (lambda (eta-2) (PreludeC-45Basics-flip arg-3 eta-0 eta-2)) eta-1))) (lambda (eta-0) eta-0) arg-5) arg-4)))
(define PreludeC-45Types-u--foldlM_Foldable_C-40EitherC-32C-36eC-41 (lambda (arg-4 arg-5 arg-6 ext-0) (PreludeC-45Types-u--foldl_Foldable_C-40EitherC-32C-36eC-41 (lambda (u--ma) (lambda (u--b) (let ((e-2 (vector-ref arg-4 1))) ((((e-2 'erased) 'erased) u--ma) (lambda (eta-0) (PreludeC-45Basics-flip arg-5 u--b eta-0)))))) (let ((e-1 (vector-ref arg-4 0))) (let ((e-5 (vector-ref e-1 1))) ((e-5 'erased) arg-6))) ext-0)))
(define PreludeC-45Types-u--join_Monad_C-40EitherC-32C-36eC-41 (lambda (arg-2) (PreludeC-45Types-u--C-62C-62C-61_Monad_C-40EitherC-32C-36eC-41 arg-2 (lambda (eta-0) eta-0))))
(define PreludeC-45InterfacesC-45Functor-u--map_Functor_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (lambda (arg-4 arg-5 ext-0 ext-1) ((((arg-4 'erased) 'erased) (lambda (arg-1) ((((arg-5 'erased) 'erased) ext-0) arg-1))) ext-1)))
(define PreludeC-45Types-u--null_Foldable_C-40EitherC-32C-36eC-41 (lambda (arg-2) (case (vector-ref arg-2 0) ((0) 1) (else 0))))
(define PreludeC-45Types-u--toList_Foldable_C-40EitherC-32C-36eC-41 (lambda (ext-0) (PreludeC-45Types-u--foldr_Foldable_C-40EitherC-32C-36eC-41 csegen-66 '() ext-0)))
(define PreludeC-45Types-u--traverse_Traversable_C-40EitherC-32C-36eC-41 (lambda (arg-4 arg-5 arg-6) (case (vector-ref arg-6 0) ((0) (let ((e-2 (vector-ref arg-6 1))) (let ((e-4 (vector-ref arg-4 1))) ((e-4 'erased) (vector 0 e-2))))) (else (let ((e-5 (vector-ref arg-6 1))) (let ((e-1 (vector-ref arg-4 0))) ((((e-1 'erased) 'erased) (lambda (eta-0) (vector 1 eta-0))) (arg-5 e-5))))))))
(define SystemC-45FileC-45ReadWrite-readLinesOnto (lambda (arg-1 arg-2 arg-3 arg-4 arg-5) (if (null? arg-4) (let ((e-1 (car arg-1))) (let ((e-5 (vector-ref e-1 0))) (let ((e-7 (vector-ref e-5 1))) ((e-7 'erased) (vector 1 (cons 0 (PreludeC-45TypesC-45List-reverse arg-2))))))) (let ((e-0 (unbox arg-4))) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (SystemC-45FileC-45ReadWrite-fEOF arg-1 arg-5)) (lambda (_-0) (cond ((equal? _-0 0) (cond ((equal? arg-3 0) (PreludeC-45InterfacesC-45Monad-u--C-62C-62C-61_Monad_ComposeC-40C-40C-46C-32C-36mC-41C-32C-36tC-41 (let ((e-7 (car arg-1))) e-7) (vector (vector (lambda (u--b) (lambda (u--a) (lambda (u--func) (lambda (arg-8945) (case (vector-ref arg-8945 0) ((0) (let ((e-6 (vector-ref arg-8945 1))) (vector 0 e-6))) (else (let ((e-6 (vector-ref arg-8945 1))) (vector 1 (u--func e-6))))))))) (lambda (u--a) (lambda (arg-9991) (vector 1 arg-9991))) (lambda (u--b) (lambda (u--a) (lambda (arg-9997) (lambda (arg-10004) (case (vector-ref arg-9997 0) ((0) (let ((e-6 (vector-ref arg-9997 1))) (vector 0 e-6))) (else (let ((e-6 (vector-ref arg-9997 1))) (case (vector-ref arg-10004 0) ((1) (let ((e-8 (vector-ref arg-10004 1))) (vector 1 (e-6 e-8)))) (else (let ((e-11 (vector-ref arg-10004 1))) (vector 0 e-11)))))))))))) (lambda (u--b) (lambda (u--a) (lambda (arg-10477) (lambda (arg-10480) (PreludeC-45Types-u--C-62C-62C-61_Monad_C-40EitherC-32C-36eC-41 arg-10477 arg-10480))))) (lambda (u--a) (lambda (arg-10491) (PreludeC-45Types-u--join_Monad_C-40EitherC-32C-36eC-41 arg-10491)))) (vector (lambda (u--b) (lambda (u--a) (lambda (u--func) (lambda (arg-8945) (case (vector-ref arg-8945 0) ((0) (let ((e-6 (vector-ref arg-8945 1))) (vector 0 e-6))) (else (let ((e-6 (vector-ref arg-8945 1))) (vector 1 (u--func e-6))))))))) (vector (lambda (u--acc) (lambda (u--elem) (lambda (u--func) (lambda (u--init) (lambda (u--input) (PreludeC-45Types-u--foldr_Foldable_C-40EitherC-32C-36eC-41 u--func u--init u--input)))))) (lambda (u--elem) (lambda (u--acc) (lambda (u--func) (lambda (u--init) (lambda (u--input) (PreludeC-45Types-u--foldl_Foldable_C-40EitherC-32C-36eC-41 u--func u--init u--input)))))) (lambda (u--elem) (lambda (arg-10980) (PreludeC-45Types-u--null_Foldable_C-40EitherC-32C-36eC-41 arg-10980))) (lambda (u--elem) (lambda (u--acc) (lambda (u--m) (lambda (i_con-0) (lambda (u--funcM) (lambda (u--init) (lambda (u--input) (PreludeC-45Types-u--foldlM_Foldable_C-40EitherC-32C-36eC-41 i_con-0 u--funcM u--init u--input)))))))) (lambda (u--elem) (lambda (arg-11009) (PreludeC-45Types-u--toList_Foldable_C-40EitherC-32C-36eC-41 arg-11009))) (lambda (u--a) (lambda (u--m) (lambda (i_con-0) (lambda (u--f) (lambda (arg-11023) (PreludeC-45Types-u--foldMap_Foldable_C-40EitherC-32C-36eC-41 i_con-0 u--f arg-11023))))))) (lambda (u--b) (lambda (u--a) (lambda (u--f) (lambda (i_con-0) (lambda (arg-14162) (lambda (arg-14169) (PreludeC-45Types-u--traverse_Traversable_C-40EitherC-32C-36eC-41 i_con-0 arg-14162 arg-14169)))))))) (SystemC-45FileC-45ReadWrite-fGetLine arg-1 arg-5) (lambda (u--str) (SystemC-45FileC-45ReadWrite-readLinesOnto arg-1 (cons u--str arg-2) 0 (e-0) arg-5))))(else (let ((e-6 (- arg-3 1))) (PreludeC-45Interfaces-C-42C-62 (vector (lambda (u--b) (lambda (u--a) (lambda (u--func) (lambda (arg-8945) (PreludeC-45InterfacesC-45Functor-u--map_Functor_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (let ((e-8 (car arg-1))) (let ((e-11 (vector-ref e-8 0))) (let ((e-14 (vector-ref e-11 0))) e-14))) (lambda (b-0) (lambda (a-0) (lambda (func-0) (lambda (arg-8946) (case (vector-ref arg-8946 0) ((0) (let ((e-7 (vector-ref arg-8946 1))) (vector 0 e-7))) (else (let ((e-7 (vector-ref arg-8946 1))) (vector 1 (func-0 e-7))))))))) u--func arg-8945))))) (lambda (u--a) (lambda (arg-9991) (PreludeC-45InterfacesC-45Applicative-u--pure_Applicative_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (let ((e-8 (car arg-1))) (let ((e-11 (vector-ref e-8 0))) e-11)) (vector (lambda (u--b) (lambda (a-0) (lambda (u--func) (lambda (arg-8945) (case (vector-ref arg-8945 0) ((0) (let ((e-7 (vector-ref arg-8945 1))) (vector 0 e-7))) (else (let ((e-7 (vector-ref arg-8945 1))) (vector 1 (u--func e-7))))))))) (lambda (a-0) (lambda (arg-9992) (vector 1 arg-9992))) (lambda (u--b) (lambda (a-0) (lambda (arg-9997) (lambda (arg-10004) (case (vector-ref arg-9997 0) ((0) (let ((e-7 (vector-ref arg-9997 1))) (vector 0 e-7))) (else (let ((e-7 (vector-ref arg-9997 1))) (case (vector-ref arg-10004 0) ((1) (let ((e-8 (vector-ref arg-10004 1))) (vector 1 (e-7 e-8)))) (else (let ((e-11 (vector-ref arg-10004 1))) (vector 0 e-11)))))))))))) arg-9991))) (lambda (u--b) (lambda (u--a) (lambda (arg-9997) (lambda (arg-10004) (PreludeC-45InterfacesC-45Applicative-u--C-60C-42C-62_Applicative_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (let ((e-8 (car arg-1))) (let ((e-11 (vector-ref e-8 0))) e-11)) (vector (lambda (b-0) (lambda (a-0) (lambda (u--func) (lambda (arg-8945) (case (vector-ref arg-8945 0) ((0) (let ((e-7 (vector-ref arg-8945 1))) (vector 0 e-7))) (else (let ((e-7 (vector-ref arg-8945 1))) (vector 1 (u--func e-7))))))))) (lambda (a-0) (lambda (arg-9991) (vector 1 arg-9991))) (lambda (b-0) (lambda (a-0) (lambda (arg-9998) (lambda (arg-10005) (case (vector-ref arg-9998 0) ((0) (let ((e-7 (vector-ref arg-9998 1))) (vector 0 e-7))) (else (let ((e-7 (vector-ref arg-9998 1))) (case (vector-ref arg-10005 0) ((1) (let ((e-8 (vector-ref arg-10005 1))) (vector 1 (e-7 e-8)))) (else (let ((e-11 (vector-ref arg-10005 1))) (vector 0 e-11)))))))))))) arg-9997 arg-10004)))))) (SystemC-45FileC-45ReadWrite-fSeekLine arg-1 arg-5) (SystemC-45FileC-45ReadWrite-readLinesOnto arg-1 arg-2 e-6 (box e-0) arg-5)))))) (else (let ((e-7 (car arg-1))) (let ((e-10 (vector-ref e-7 0))) (let ((e-12 (vector-ref e-10 1))) ((e-12 'erased) (vector 1 (cons 1 (PreludeC-45TypesC-45List-reverse arg-2)))))))))))))))))
(define SystemC-45FileC-45ReadWrite-readFilePage (lambda (arg-1 arg-2 arg-3 arg-4) (SystemC-45FileC-45Handle-withFile arg-1 arg-4 0 (lambda (eta-0) (let ((e-1 (car arg-1))) (let ((e-5 (vector-ref e-1 0))) (let ((e-7 (vector-ref e-5 1))) ((e-7 'erased) eta-0))))) (lambda (eta-0) (SystemC-45FileC-45ReadWrite-readLinesOnto arg-1 '() arg-2 arg-3 eta-0)))))
(define Builtin-snd (lambda (arg-2) (let ((e-3 (cdr arg-2))) e-3)))
(define SystemC-45FileC-45ReadWrite-readFile (lambda (arg-1 ext-0) (let ((e-1 (car arg-1))) (let ((e-5 (vector-ref e-1 0))) (let ((e-8 (vector-ref e-5 0))) ((((e-8 'erased) 'erased) (lambda (eta-0) (case (vector-ref eta-0 0) ((0) (let ((e-9 (vector-ref eta-0 1))) (vector 0 e-9))) (else (let ((e-9 (vector-ref eta-0 1))) (vector 1 (PreludeC-45Types-fastConcat (Builtin-snd e-9)))))))) (SystemC-45FileC-45ReadWrite-readFilePage arg-1 0 (DataC-45Fuel-forever) ext-0)))))))
(define PrimIO-unsafeCreateWorld (lambda (arg-1) (arg-1 #f)))
(define PrimIO-unsafePerformIO (lambda (arg-1) (PrimIO-unsafeCreateWorld (lambda (u--w) (arg-1 u--w)))))
(define SystemC-45Errno-strerror (lambda (arg-0) (PrimIO-unsafePerformIO (lambda (eta-0) (SystemC-45Errno-prim__strerror arg-0 eta-0)))))
(define SystemC-45FileC-45Error-u--show_Show_FileError (lambda (arg-0) (case (vector-ref arg-0 0) ((0) (let ((e-0 (vector-ref arg-0 1))) (SystemC-45Errno-strerror e-0))) ((1) "File Read Error") ((2) "File Write Error") ((3) "File Not Found") ((4) "Permission Denied") (else "File Exists"))))
(define LinearRegression-loadStandardizedData (lambda (ext-0) (let ((act-1 (PreludeC-45IO-prim__putStr " Loading standardized training data from CSV file...\xa;" ext-0))) (let ((act-2 (PreludeC-45IO-prim__putStr " Reading from: data/california_train_standardized.csv\xa;" ext-0))) (let ((act-3 ((SystemC-45FileC-45ReadWrite-readFile (force csegen-13) "data/california_train_standardized.csv") ext-0))) (case (vector-ref act-3 0) ((1) (let ((e-2 (vector-ref act-3 1))) (let ((u--allLines (DataC-45String-lines e-2))) (if (null? u--allLines) (let ((act-4 (PreludeC-45IO-prim__putStr "\x274c; Error: Empty CSV file\xa;" ext-0))) '()) (let ((e-3 (cdr u--allLines))) (let ((act-4 (PreludeC-45IO-prim__putStr (string-append (string-append " Found " (string-append (PreludeC-45Show-u--show_Show_Nat (PreludeC-45TypesC-45List-lengthTR e-3)) " data lines (excluding header)")) "\xa;") ext-0))) (let ((act-5 (PreludeC-45IO-prim__putStr (string-append (string-append " Parsing first " (string-append (PreludeC-45Show-u--show_Show_Nat LinearRegression-SAMPLE_SIZE) " samples...")) "\xa;") ext-0))) (((let ((u--sampleLines (DataC-45List-take LinearRegression-SAMPLE_SIZE e-3))) (let ((sc2 (LinearRegression-parseAllLines u--sampleLines))) (if (null? sc2) (lambda () (lambda (world-0) (let ((act-6 (PreludeC-45IO-prim__putStr "\x274c; Error: Failed to parse CSV data\xa;" world-0))) '()))) (let ((e-5 (unbox sc2))) (let ((e-7 (car e-5))) (let ((e-6 (cdr e-5))) (lambda () (lambda (world-0) (let ((act-6 (PreludeC-45IO-prim__putStr (string-append (string-append " Successfully loaded " (string-append (PreludeC-45Show-u--show_Show_Nat LinearRegression-SAMPLE_SIZE) " training samples")) "\xa;") world-0))) (let ((act-7 (PreludeC-45IO-prim__putStr (string-append (string-append " Features: " (string-append (PreludeC-45Show-u--show_Show_Nat LinearRegression-FEATURES) " dimensions")) "\xa;") world-0))) (let ((act-8 (PreludeC-45IO-prim__putStr " Data preprocessed with StandardScaler (same as Python)\xa;" world-0))) (box (cons e-7 e-6)))))))))))))) ext-0)))))))) (else (let ((e-5 (vector-ref act-3 1))) (let ((act-4 (PreludeC-45IO-prim__putStr (string-append (string-append "\x274c; Error reading CSV file: " (SystemC-45FileC-45Error-u--show_Show_FileError e-5)) "\xa;") ext-0))) '())))))))))
(define LinearRegression-runLinearRegressionExperiment (lambda (ext-0) (let ((act-1 (PreludeC-45IO-prim__putStr " Shape-Safe Linear Regression - Fair Comparison Implementation\xa;" ext-0))) (let ((act-2 (PreludeC-45IO-prim__putStr "=============================================================\xa;" ext-0))) (let ((act-3 (PreludeC-45IO-prim__putStr " Configuration (matching Python implementations):\xa;" ext-0))) (let ((act-4 (PreludeC-45IO-prim__putStr (string-append (string-append "   Features: " (string-append (PreludeC-45Show-u--show_Show_Nat LinearRegression-FEATURES) " (California Housing)")) "\xa;") ext-0))) (let ((act-5 (PreludeC-45IO-prim__putStr (string-append (string-append "   Learning rate: " (PreludeC-45Show-u--show_Show_Double LinearRegression-LEARNING_RATE)) "\xa;") ext-0))) (let ((act-6 (PreludeC-45IO-prim__putStr (string-append (string-append "   Max iterations: " (PreludeC-45Show-u--show_Show_Nat LinearRegression-MAX_ITERATIONS)) "\xa;") ext-0))) (let ((act-7 (PreludeC-45IO-prim__putStr (string-append (string-append "   Regularization: " (PreludeC-45Show-u--show_Show_Double LinearRegression-REGULARIZATION)) "\xa;") ext-0))) (let ((act-8 (PreludeC-45IO-prim__putStr "\xa; Loading standardized California Housing data...\xa;" ext-0))) (let ((act-9 (LinearRegression-loadStandardizedData ext-0))) (if (null? act-9) (let ((act-10 (PreludeC-45IO-prim__putStr "\x274c; Failed to load standardized data\xa;" ext-0))) ((System-exitWith (force csegen-13) (cons 1 'erased)) ext-0)) (let ((e-1 (unbox act-9))) (let ((e-5 (car e-1))) (let ((e-6 (cdr e-1))) (let ((act-10 (PreludeC-45IO-prim__putStr (string-append (string-append " Loaded " (string-append (PreludeC-45Show-u--show_Show_Nat LinearRegression-SAMPLE_SIZE) " training samples")) "\xa;") ext-0))) (let ((act-11 (PreludeC-45IO-prim__putStr (string-append (string-append " Input shape verified: [" (string-append (PreludeC-45Show-u--show_Show_Nat LinearRegression-SAMPLE_SIZE) (string-append ", " (string-append (PreludeC-45Show-u--show_Show_Nat LinearRegression-FEATURES) "]")))) "\xa;") ext-0))) (let ((act-12 (PreludeC-45IO-prim__putStr " Data preprocessed with StandardScaler (same as Python)\xa;" ext-0))) (let ((act-13 (PreludeC-45IO-prim__putStr "\xa; Training shape-safe linear regression...\xa;" ext-0))) (((let ((u--initialModel LinearRegression-initializeModel)) (lambda () (lambda (world-0) (let ((act-14 (PreludeC-45IO-prim__putStr "   Initialized weights and bias\xa;" world-0))) (((let ((u--trainedModel (LinearRegression-trainModel 1000 e-5 e-6 u--initialModel LinearRegression-MAX_ITERATIONS))) (lambda () (lambda (world-1) (let ((act-15 (PreludeC-45IO-prim__putStr (string-append (string-append "   Completed " (string-append (PreludeC-45Show-u--show_Show_Nat LinearRegression-MAX_ITERATIONS) " training iterations")) "\xa;") world-1))) (((let ((_-0 (box (cons e-5 e-6)))) (LinearRegression-case--caseC-32blockC-32inC-32runLinearRegressionExperiment-8805 e-5 e-6 _-0 u--initialModel u--trainedModel (LinearRegression-evaluateModel 1000 e-5 e-6 u--trainedModel)))) world-1)))))) world-0)))))) ext-0))))))))))))))))))))
(define LinearRegression-main (lambda (ext-0) (LinearRegression-runLinearRegressionExperiment ext-0)))
(define PreludeC-45Types-prim__integerToNat (lambda (arg-0) (let ((sc0 (or (and (<= 0 arg-0) 1) 0))) (cond ((equal? sc0 0) 0)(else arg-0)))))
(define PreludeC-45EqOrd-compareInteger (lambda (ext-0 ext-1) (PreludeC-45EqOrd-u--compare_Ord_Integer ext-0 ext-1)))
(collect-request-handler
  (let* ([gc-counter 1]
         [log-radix 2]
         [radix-mask (sub1 (bitwise-arithmetic-shift 1 log-radix))]
         [major-gc-factor 2]
         [trigger-major-gc-allocated (* major-gc-factor (bytes-allocated))])
    (lambda ()
      (cond
        [(>= (bytes-allocated) trigger-major-gc-allocated)
         ;; Force a major collection if memory use has doubled
         (collect (collect-maximum-generation))
         (blodwen-run-finalisers)
         (set! trigger-major-gc-allocated (* major-gc-factor (bytes-allocated)))]
        [else
         ;; Imitate the built-in rule, but without ever going to a major collection
         (let ([this-counter gc-counter])
           (if (> (add1 this-counter)
                  (bitwise-arithmetic-shift-left 1 (* log-radix (sub1 (collect-maximum-generation)))))
               (set! gc-counter 1)
               (set! gc-counter (add1 this-counter)))
           (collect
            ;; Find the minor generation implied by the counter
            (let loop ([c this-counter] [gen 0])
              (cond
                [(zero? (bitwise-and c radix-mask))
                 (loop (bitwise-arithmetic-shift-right c log-radix)
                       (add1 gen))]
                [else
                 gen]))))]))))
(PrimIO-unsafePerformIO (lambda (eta-0) (LinearRegression-main eta-0)))
  (collect 4)
  (blodwen-run-finalisers)
  
  )