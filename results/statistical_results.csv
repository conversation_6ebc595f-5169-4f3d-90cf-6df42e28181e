implementation,iteration,train_mse,test_mse,train_r2,test_r2,execution_time,peak_memory_mb
Manual,0,0.5043918950679702,0.5977677924671712,0.6073865689482558,0.5898218819897776,0.04031801223754883,0.07622432708740234
Sklearn,0,0.4727940114073948,0.5916563777925995,0.6319820345757926,0.5940154310588059,0.00555109977722168,0.17151355743408203
PyTorch,0,0.48081211977505994,0.5859749523539973,0.6257408220036724,0.5979139288089156,3.852400064468384,46.50828170776367
Manual,1,0.5032628481750938,0.5968485420336326,0.6082654073649016,0.5904526560421111,0.04779934883117676,0.04505729675292969
Sklearn,1,0.4727940114073948,0.5916563777925995,0.6319820345757926,0.5940154310588059,0.004869937896728516,0.151031494140625
PyTorch,1,0.48703379487938225,0.5908599982436304,0.6208979344920322,0.5945618931946627,0.40026307106018066,0.04667377471923828
Manual,2,0.5043520666059665,0.5976742966720163,0.6074175710108856,0.5898860372182488,0.04079318046569824,0.044628143310546875
Sklearn,2,0.4727940114073948,0.5916563777925995,0.6319820345757926,0.5940154310588059,0.005019664764404297,0.14931488037109375
PyTorch,2,0.47771203478131163,0.5841251683263371,0.6281538960793047,0.5991832192269548,0.42018651962280273,0.038178443908691406
Manual,3,0.504083286420882,0.5974475006239801,0.6076267867252867,0.5900416608187973,0.04651236534118652,0.044628143310546875
Sklearn,3,0.4727940114073948,0.5916563777925995,0.6319820345757926,0.5940154310588059,0.006493568420410156,0.14947986602783203
PyTorch,3,0.4846786011910463,0.5873874343059512,0.6227311928845851,0.5969447076563597,0.42156386375427246,0.037720680236816406
Manual,4,0.5042941730791796,0.597629876246385,0.607462634732983,0.5899165177607208,0.047110557556152344,0.044628143310546875
Sklearn,4,0.4727940114073948,0.5916563777925995,0.6319820345757926,0.5940154310588059,0.005030393600463867,0.14931488037109375
PyTorch,4,0.4861165795160804,0.5893109577374912,0.6216118854383511,0.5956248185103732,0.4195871353149414,0.037720680236816406
Manual,5,0.5040411466240322,0.5974633454166383,0.6076595879070399,0.5900307883908835,0.04738450050354004,0.044628143310546875
Sklearn,5,0.4727940114073948,0.5916563777925995,0.6319820345757926,0.5940154310588059,0.00515294075012207,0.14931488037109375
PyTorch,5,0.4817784658501017,0.585484948237649,0.6249886282198005,0.5982501613205928,0.42205190658569336,0.037720680236816406
Manual,6,0.5038886759112559,0.5973503395806476,0.6077782695319102,0.5901083310783868,0.04754161834716797,0.04589557647705078
Sklearn,6,0.4727940114073948,0.5916563777925995,0.6319820345757926,0.5940154310588059,0.005074262619018555,0.14931488037109375
PyTorch,6,0.48203339324161004,0.5855504434060463,0.6247901953765533,0.5982052196471697,0.42368650436401367,0.037720680236816406
Manual,7,0.5042792642860539,0.5976430919949152,0.6074742395832575,0.5899074493382856,0.04824090003967285,0.044884681701660156
Sklearn,7,0.4727940114073948,0.5916563777925995,0.6319820345757926,0.5940154310588059,0.006510734558105469,0.14931488037109375
PyTorch,7,0.48247914453887913,0.5864017519020268,0.624443227179943,0.5976210661997479,0.42203760147094727,0.037720680236816406
Manual,8,0.504114998126254,0.5975030919691585,0.6076021026620169,0.5900035149808478,0.0475316047668457,0.044628143310546875
Sklearn,8,0.4727940114073948,0.5916563777925995,0.6319820345757926,0.5940154310588059,0.005015134811401367,0.14931488037109375
PyTorch,8,0.4797193669279096,0.5850277390360065,0.6265914095107407,0.5985638905181675,0.42500948905944824,0.037720680236816406
Manual,9,0.5035513753501984,0.5970777222507658,0.6080408207978352,0.5902953964651758,0.0485835075378418,0.044628143310546875
Sklearn,9,0.4727940114073948,0.5916563777925995,0.6319820345757926,0.5940154310588059,0.005494832992553711,0.14931488037109375
PyTorch,9,0.4805219222852768,0.5843780031490065,0.6259667087263983,0.5990097282610032,0.42125439643859863,0.037720680236816406
