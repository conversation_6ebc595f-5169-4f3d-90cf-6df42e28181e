Linear Regression Results - California Housing
==================================================

Configuration:
  Learning rate: 0.01
  Max iterations: 1000
  Regularization: 0.001

Manual Implementation:
  Train MSE: 0.504514
  Test MSE: 0.597924
  Train R²: 0.6073
  Test R²: 0.5897
  Execution time: 0.2149s
  Peak memory: 0.08 MB
  Shape errors: 0

Pytorch Implementation:
  Train MSE: 0.484766
  Test MSE: 0.587381
  Train R²: 0.6227
  Test R²: 0.5969
  Execution time: 4.3326s
  Peak memory: 45.76 MB
  Shape errors: 0

Sklearn Implementation:
  Train MSE: 0.472794
  Test MSE: 0.591655
  Train R²: 0.6320
  Test R²: 0.5940
  Execution time: 0.0125s
  Peak memory: 0.14 MB
  Shape errors: 0

