CONTROLLED COMPARISON RESULTS
==================================================
Implementation Test MSE   Test R²  Time(s) 
--------------------------------------------------
Manual       0.491056   0.5871   0.0607  
PyTorch      0.478287   0.5978   1.1432  
Sklearn      0.478652   0.5975   0.0018  
Idris        0.514845   0.5671   0.0000  
Spidr        5.051106   -3.2476  0.0000  

Key Findings:
 All implementations use identical standardized dataset
 Consistent preprocessing with SHA-256 verification
 Shape safety: Idris (compile-time) vs Python (runtime)
 Fair evaluation on identical test set
