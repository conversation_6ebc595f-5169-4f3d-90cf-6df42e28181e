{"dataset_config": {"subset_size": 1429, "test_size": 0.3, "random_state": 123, "stratify_bins": 5, "features": 8, "target_name": "target"}, "feature_names": ["MedInc", "HouseAge", "AveRooms", "AveBedrms", "Population", "AveOccup", "Latitude", "Longitude"], "scaler_mean": [3.9408727781665487, 28.92862141357593, 5.4405943278895235, 1.0977455318686635, 1489.585024492652, 3.371150783765948, 35.620202939118286, -119.55191742477261], "scaler_scale": [1.9259306304386374, 12.279367602133131, 2.0148434344563553, 0.3530895943200674, 1464.1615280936603, 15.809619066261265, 2.1860274595684213, 2.031981756173529], "train_samples": 1000, "test_samples": 429, "total_samples": 1429}