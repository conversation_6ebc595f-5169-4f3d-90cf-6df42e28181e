-- Idris data extracted from standardized California Housing dataset
-- First 50 samples from training set (same preprocessing as Python)

let standardizedInputs : Matrix SAMPLE_SIZE FEATURES = [
    [0.14460782, -0.17790253, -0.38568709, -0.15935610, 2.02730820, 4.24491765, -0.87173864, 0.84731976],
    [0.13797541, 0.53963009, -0.09281217, -0.15142968, 1.23203707, 1.09623285, -0.72458515, 0.64636642],
    [-0.56505969, -1.37379024, -0.21133403, -0.11089243, 0.03092229, 0.36692122, 0.87511571, -0.66987796],
    [0.27988786, 1.89496949, 0.01753845, -0.15373906, -0.22444000, -0.17401266, 1.00328166, -1.49881049],
    [-0.07720933, -0.17790253, -0.19182994, -0.00814009, -0.31381680, -1.38463945, -0.70085072, 0.54086592],
    [0.78305592, 1.09771102, 0.25064517, -0.06969386, -0.43511389, -0.61593115, 0.72796222, -1.24259498],
    [-0.98158535, 1.89496949, 0.15363146, 0.07886403, -0.74246065, -0.30876517, 1.01277543, -1.45861983],
    [1.52925430, -0.73598346, 0.39145288, -0.09694891, 0.23521212, 0.31115955, 0.65201203, -1.09187998],
    [-0.58385151, -0.49680592, -0.24397865, -0.06184738, -0.38768947, -0.51299854, 1.57765496, -0.77035463],
    [0.08960096, 1.01798517, 0.58903766, 0.34737698, -0.40775365, -0.19227209, 1.41626081, -1.66459700],
    [1.74464960, -1.29406439, 0.22764638, -0.20055143, -0.34664910, -0.21419399, -0.98566393, 0.90760576],
    [-1.60055734, -1.21433854, -0.92967644, -0.07934666, -0.74610868, -2.01518055, -0.70085072, 0.63129492],
    [-0.71907891, -0.17790253, -0.47812783, -0.14299911, 0.08564278, -0.00750120, -0.87648553, 0.77698609],
    [-0.07499853, 1.17743686, -0.03828410, -0.19685326, -0.63757971, 0.43775367, -0.77680091, 0.59612809],
    [0.91323006, 1.25716271, 0.25645044, -0.08438784, -0.53361078, -0.67966893, -0.78629468, 0.55091358],
    [-0.46867924, 1.17743686, -0.33524090, -0.13691610, -0.08307873, 0.86714676, -0.73407892, 0.71670009],
    [-1.12528750, -1.37379024, 4.40275571, 5.66376365, -1.05254343, -1.00687859, 2.38462573, -0.63471113],
    [0.85974970, -0.17790253, 0.20662493, -0.07795272, -0.21805594, 0.44004135, -0.85749798, 0.60115192],
    [-0.99337630, -1.21433854, -0.21534780, -0.13305985, -0.14418328, 0.38165629, -0.90971374, 1.21405961],
    [0.01906584, -0.09817669, 0.00347239, -0.16177137, 0.91009818, -0.39393115, 1.44474213, -0.86580747],
    [1.22295292, 0.38017840, 0.43289230, -0.30214335, -0.20984787, 0.09860471, -0.81477600, 0.79708143],
    [0.01622338, -1.69269362, -0.26077698, -0.20509248, 0.43494191, -0.17752929, 1.28809487, -1.23254732],
    [-1.18108393, -0.89543515, 17.36801757, 18.76160343, -1.23220904, -1.38879742, -0.78629468, 2.52025633],
    [0.02769849, -1.61296778, 0.28409343, 0.43469793, -0.08399074, -0.81112644, -0.59641921, 0.49062758],
    [0.44622441, -0.09817669, 0.29917600, -0.02871559, -0.51172258, 0.73528978, 1.12670072, -0.73518780],
    [-0.96953122, -0.25762838, -0.90123777, -0.05883204, 5.27770536, -0.50034674, -0.73882581, 0.61622342],
    [-0.34829580, -0.33735423, -0.40455175, -0.09496821, 0.95205055, -0.84505433, -0.72458515, 0.69158092],
    [-1.25646176, -1.93187116, -0.54164278, 0.34000258, -0.64487577, -1.21527830, -0.74357270, 0.88751043],
    [-1.12665609, -0.41708007, -0.20604936, 0.15368831, -0.86558175, -0.55387267, 1.56341430, -0.25289978],
    [-1.34489385, 1.33688856, -0.48553984, -0.21370622, 0.06193057, 0.89735456, -0.78154779, 0.62124725],
    [-0.34845371, -0.65625761, -0.60993023, -0.02386744, -0.77620495, -1.46621247, 1.00802854, -1.39330999],
    [-1.12044479, -0.01845084, -0.82639875, 0.02704104, 0.02636225, -0.67781419, -0.72458515, 0.59612809],
    [-1.54607685, -1.05488685, 0.22723986, 0.23892274, -1.16198441, -0.90949902, -1.07585478, 1.80687197],
    [0.65093417, -0.81570931, -0.07708168, -0.25080788, 0.10297094, 1.37631740, 0.75644354, -1.14211831],
    [-0.79650963, 0.77880763, -0.69317534, -0.11863483, -0.41048967, -1.94883744, -0.88123242, 0.67148559],
    [0.19671959, -1.85214532, 0.05819931, -0.17181108, 4.04649432, 0.59200842, -0.50622836, 0.72172392],
    [1.66985079, 1.09771102, 0.39470688, -0.12053744, -0.23264808, 0.58526297, -0.70559760, 0.72674776],
    [0.89933359, -1.93187116, 0.11555539, -0.02202396, -0.54729090, -1.03510697, -1.00939836, 0.90760576],
    [0.27930884, 1.89496949, -0.06621814, -0.05128440, 0.04642643, 0.65032323, 0.99853477, -1.47369133],
    [1.69580140, -1.93187116, 0.26866170, -0.11452910, 1.34330206, -0.25039039, -0.63439430, 0.47053225],
    [-0.51568511, 1.25716271, 0.07918879, 0.04346521, -0.56097102, -0.75044681, 0.33397061, 0.10379240],
    [-0.50984227, -0.97516100, -0.03982917, -0.14745629, -0.71327639, -1.13985844, -1.31794600, 1.31453628],
    [0.45275154, -1.05488685, 0.34005862, -0.10906137, 0.89824207, 0.03986376, -0.76730713, 1.25425028],
    [0.55865949, 0.53963009, -0.11953923, -0.25469262, -0.61660352, 0.24994103, -0.80528223, 0.75689076],
    [0.95602487, -1.29406439, -0.04970621, -0.10381407, -0.36580127, -0.11237641, -1.01889213, 0.93774877],
    [3.44049281, -1.61296778, 0.81882814, -0.19685326, -0.23538410, 0.68934711, -0.97142327, 0.91262960],
    [-0.57442928, 0.85853348, 0.17098707, 0.17710812, -1.14009622, 0.44370163, -0.80528223, 0.65139025],
    [-0.11131885, -1.05488685, 0.85238025, 0.71566091, -0.85098962, -0.57796081, 2.21848469, -0.79547380],
    [-0.66975697, -0.97516100, 0.02257578, -0.04042755, -0.45791410, -0.54061441, -0.71983826, 0.93774877],
    [1.26595829, 1.57606610, 0.13326125, -0.32037953, -0.85463766, -0.48764660, 0.99853477, -1.49881049]
  ]

let standardizedTargets : Vector SAMPLE_SIZE = [
    2.22300000, 2.52100000, 1.10300000, 3.17700000, 4.47400000, 2.79900000, 2.62500000, 4.12600000, 1.53200000, 2.01700000,
    2.62100000, 1.87500000, 2.36100000, 1.08900000, 3.63500000, 2.11400000, 1.18300000, 3.61700000, 0.90500000, 1.33000000,
    3.36900000, 1.40300000, 0.87500000, 3.33700000, 1.76900000, 1.77500000, 2.52400000, 1.37500000, 0.96100000, 0.99200000,
    1.00000000, 4.50000000, 0.67500000, 2.17100000, 1.65600000, 1.45500000, 4.37300000, 2.45400000, 2.70000000, 3.41200000,
    0.77000000, 1.58300000, 1.35200000, 1.73900000, 2.75900000, 5.00001000, 1.08900000, 1.13400000, 1.46900000, 4.38500000
  ]

-- Verification: 50 samples, 8 features
-- Target range: [0.675, 5.000]
-- Feature means: [ 0.01341776 -0.22414352  0.39922163  0.44013341 -0.02067913 -0.16781797
 -0.09334414  0.18005419]
-- Feature stds: [1.01276438 1.14173314 2.53283286 2.7434097  1.16650105 0.99361318
 1.01523181 0.99461231]
