#!/usr/bin/env python3
"""
Generate comprehensive performance visualization plots for the linear regression comparative study.
Uses real performance data from our controlled experiments.
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns

# Set style for better-looking plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def create_comprehensive_performance_plots():
    """Create comprehensive performance comparison plots with real data."""
    
    # Real performance data from our comparative study
    implementations = ['Manual\nPython', 'PyTorch', 'Sklearn', 'Idris\n(Standard)', 'Spidr\n(Shape-Safe)']
    test_mse = [0.597858, 0.585739, 0.591655, 0.597649, 3.994357]
    test_r2 = [0.5898, 0.5981, 0.5940, 0.5899, -1.7409]
    exec_time = [0.1054, 4.5160, 0.0100, 0.7130, 0.2350]
    memory = [0.04, 52.00, 0.15, 42.45, 23.84]
    
    # Create figure with subplots
    fig = plt.figure(figsize=(20, 12))
    
    # Main title
    fig.suptitle('Linear Regression Implementation Comparison\nReal Performance Data from Controlled Study', 
                 fontsize=18, fontweight='bold', y=0.95)
    
    colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6']
    
    # 1. Accuracy Comparison - MSE
    ax1 = plt.subplot(2, 3, 1)
    competitive_impls = implementations[:4]  # Exclude Spidr for this view
    competitive_mse = test_mse[:4]
    competitive_colors = colors[:4]
    
    bars1 = ax1.bar(competitive_impls, competitive_mse, color=competitive_colors, alpha=0.8, edgecolor='black', linewidth=1)
    ax1.set_title('Test MSE - Competitive Implementations\n(Lower is Better)', fontweight='bold', fontsize=12)
    ax1.set_ylabel('Mean Squared Error', fontsize=10)
    ax1.tick_params(axis='x', rotation=45)
    ax1.set_ylim(0.58, 0.605)
    ax1.grid(True, alpha=0.3)
    
    # Add value labels
    for bar, value in zip(bars1, competitive_mse):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                f'{value:.4f}', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    # Highlight Idris as shape-safe
    bars1[3].set_edgecolor('red')
    bars1[3].set_linewidth(3)
    
    # 2. R² Score Comparison
    ax2 = plt.subplot(2, 3, 2)
    competitive_r2 = test_r2[:4]
    
    bars2 = ax2.bar(competitive_impls, competitive_r2, color=competitive_colors, alpha=0.8, edgecolor='black', linewidth=1)
    ax2.set_title('Test R^2 Score - Competitive Implementations\n(Higher is Better)', fontweight='bold', fontsize=12)
    ax2.set_ylabel('R^2 Score', fontsize=10)
    ax2.tick_params(axis='x', rotation=45)
    ax2.set_ylim(0.585, 0.605)
    ax2.grid(True, alpha=0.3)
    
    # Add value labels
    for bar, value in zip(bars2, competitive_r2):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                f'{value:.4f}', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    # Highlight Idris as shape-safe
    bars2[3].set_edgecolor('red')
    bars2[3].set_linewidth(3)
    
    # 3. Execution Time Comparison
    ax3 = plt.subplot(2, 3, 3)
    bars3 = ax3.bar(implementations, exec_time, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    ax3.set_title('Execution Time\n(Lower is Better)', fontweight='bold', fontsize=12)
    ax3.set_ylabel('Time (seconds)', fontsize=10)
    ax3.tick_params(axis='x', rotation=45)
    ax3.grid(True, alpha=0.3)
    
    # Add value labels
    for bar, value in zip(bars3, exec_time):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + max(exec_time)*0.02,
                f'{value:.3f}s', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    # Highlight shape-safe implementations
    bars3[3].set_edgecolor('red')
    bars3[3].set_linewidth(3)
    bars3[4].set_edgecolor('red')
    bars3[4].set_linewidth(3)
    
    # 4. Memory Usage Comparison
    ax4 = plt.subplot(2, 3, 4)
    bars4 = ax4.bar(implementations, memory, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    ax4.set_title('Peak Memory Usage', fontweight='bold', fontsize=12)
    ax4.set_ylabel('Memory (MB)', fontsize=10)
    ax4.tick_params(axis='x', rotation=45)
    ax4.grid(True, alpha=0.3)
    
    # Add value labels
    for bar, value in zip(bars4, memory):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + max(memory)*0.02,
                f'{value:.1f}MB', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    # Highlight shape-safe implementations
    bars4[3].set_edgecolor('red')
    bars4[3].set_linewidth(3)
    bars4[4].set_edgecolor('red')
    bars4[4].set_linewidth(3)
    
    # 5. Performance vs Accuracy Scatter Plot
    ax5 = plt.subplot(2, 3, 5)
    scatter = ax5.scatter(competitive_mse, [exec_time[i] for i in range(4)], 
                         c=competitive_colors, s=200, alpha=0.8, edgecolors='black', linewidth=2)
    
    # Add labels for each point
    for i, impl in enumerate(competitive_impls):
        ax5.annotate(impl.replace('\n', ' '), 
                    (competitive_mse[i], exec_time[i]),
                    xytext=(10, 10), textcoords='offset points',
                    fontsize=8, fontweight='bold')
    
    ax5.set_xlabel('Test MSE (Lower is Better)', fontsize=10)
    ax5.set_ylabel('Execution Time (seconds)', fontsize=10)
    ax5.set_title('Performance vs Accuracy Trade-off', fontweight='bold', fontsize=12)
    ax5.grid(True, alpha=0.3)
    
    # 6. Shape Safety Benefits
    ax6 = plt.subplot(2, 3, 6)
    shape_safe_counts = [3, 2]  # 3 non-shape-safe, 2 shape-safe
    labels = ['Runtime\nShape Checks', 'Compile-time\nShape Safety']
    colors_pie = ['#ff6b6b', '#4ecdc4']
    
    wedges, texts, autotexts = ax6.pie(shape_safe_counts, labels=labels, colors=colors_pie, 
                                      autopct='%1.0f%%', startangle=90, textprops={'fontsize': 10})
    ax6.set_title('Shape Safety Distribution', fontweight='bold', fontsize=12)
    
    # Add legend
    legend_elements = [plt.Rectangle((0,0),1,1, facecolor='none', edgecolor='red', linewidth=3, label='Shape-Safe Implementation')]
    fig.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.92), fontsize=10)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.90)
    plt.savefig('../results/comprehensive_performance_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ Saved comprehensive performance analysis plot")

if __name__ == "__main__":
    print("🎨 Generating comprehensive performance visualizations...")
    create_comprehensive_performance_plots()
    print("🎉 All visualizations generated successfully!")
