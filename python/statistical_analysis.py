#!/usr/bin/env python3
"""
Statistical Analysis for Linear Regression Shape Safety Experiment
Runs multiple iterations for statistical validity and confidence intervals
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score
import torch
import torch.nn as nn
import torch.optim as optim
import time
import tracemalloc
from typing import Dict, List, Tuple
import os
from dataclasses import dataclass
from unified_data_loader import load_standardized_data
import warnings
warnings.filterwarnings("ignore")

@dataclass
class IterationResult:
    """Results from a single iteration."""
    implementation: str
    iteration: int
    train_mse: float
    test_mse: float
    train_r2: float
    test_r2: float
    execution_time: float
    peak_memory_mb: float

class StatisticalAnalyzer:
    """Performs statistical analysis with multiple iterations."""
    
    def __init__(self, n_iterations: int = 10):
        self.n_iterations = n_iterations
        self.results = []
        
    def run_manual_implementation(self, X_train, y_train, X_test, y_test, iteration):
        """Run manual implementation for one iteration."""
        tracemalloc.start()
        start_time = time.time()
        
        # Simple gradient descent
        weights = np.random.normal(0, 0.01, (X_train.shape[1], 1))
        bias = 0.0
        learning_rate = 0.01
        
        for _ in range(1000):
            predictions = X_train @ weights + bias
            error = predictions.flatten() - y_train
            
            # Gradients
            dw = X_train.T @ error.reshape(-1, 1) / len(y_train)
            db = np.mean(error)
            
            # Update
            weights -= learning_rate * dw
            bias -= learning_rate * db
        
        # Evaluate
        train_pred = (X_train @ weights + bias).flatten()
        test_pred = (X_test @ weights + bias).flatten()
        
        train_mse = mean_squared_error(y_train, train_pred)
        test_mse = mean_squared_error(y_test, test_pred)
        train_r2 = r2_score(y_train, train_pred)
        test_r2 = r2_score(y_test, test_pred)
        
        execution_time = time.time() - start_time
        _, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        peak_mb = peak / (1024 * 1024)
        
        return IterationResult("Manual", iteration, train_mse, test_mse, 
                             train_r2, test_r2, execution_time, peak_mb)
    
    def run_sklearn_implementation(self, X_train, y_train, X_test, y_test, iteration):
        """Run sklearn implementation for one iteration."""
        tracemalloc.start()
        start_time = time.time()
        
        model = LinearRegression()
        model.fit(X_train, y_train)
        
        train_pred = model.predict(X_train)
        test_pred = model.predict(X_test)
        
        train_mse = mean_squared_error(y_train, train_pred)
        test_mse = mean_squared_error(y_test, test_pred)
        train_r2 = r2_score(y_train, train_pred)
        test_r2 = r2_score(y_test, test_pred)
        
        execution_time = time.time() - start_time
        _, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        peak_mb = peak / (1024 * 1024)
        
        return IterationResult("Sklearn", iteration, train_mse, test_mse,
                             train_r2, test_r2, execution_time, peak_mb)
    
    def run_pytorch_implementation(self, X_train, y_train, X_test, y_test, iteration):
        """Run PyTorch implementation for one iteration."""
        tracemalloc.start()
        start_time = time.time()
        
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train)
        y_train_tensor = torch.FloatTensor(y_train)
        X_test_tensor = torch.FloatTensor(X_test)
        
        # Simple linear model
        model = nn.Linear(X_train.shape[1], 1)
        optimizer = optim.SGD(model.parameters(), lr=0.01)
        criterion = nn.MSELoss()
        
        # Training
        for _ in range(1000):
            optimizer.zero_grad()
            predictions = model(X_train_tensor).flatten()
            loss = criterion(predictions, y_train_tensor)
            loss.backward()
            optimizer.step()
        
        # Evaluate
        model.eval()
        with torch.no_grad():
            train_pred = model(X_train_tensor).numpy().flatten()
            test_pred = model(X_test_tensor).numpy().flatten()
        
        train_mse = mean_squared_error(y_train, train_pred)
        test_mse = mean_squared_error(y_test, test_pred)
        train_r2 = r2_score(y_train, train_pred)
        test_r2 = r2_score(y_test, test_pred)
        
        execution_time = time.time() - start_time
        _, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        peak_mb = peak / (1024 * 1024)
        
        return IterationResult("PyTorch", iteration, train_mse, test_mse,
                             train_r2, test_r2, execution_time, peak_mb)
    
    def run_statistical_analysis(self):
        """Run multiple iterations and compute statistics."""
        print(f"Running {self.n_iterations} iterations for statistical analysis...")
        
        # Load data once
        X_train, X_test, y_train, y_test = load_standardized_data()
        
        for i in range(self.n_iterations):
            print(f"Iteration {i+1}/{self.n_iterations}")
            
            # Run each implementation
            self.results.append(self.run_manual_implementation(X_train, y_train, X_test, y_test, i))
            self.results.append(self.run_sklearn_implementation(X_train, y_train, X_test, y_test, i))
            self.results.append(self.run_pytorch_implementation(X_train, y_train, X_test, y_test, i))
        
        # Convert to DataFrame for analysis
        df = pd.DataFrame([
            {
                'implementation': r.implementation,
                'iteration': r.iteration,
                'train_mse': r.train_mse,
                'test_mse': r.test_mse,
                'train_r2': r.train_r2,
                'test_r2': r.test_r2,
                'execution_time': r.execution_time,
                'peak_memory_mb': r.peak_memory_mb
            }
            for r in self.results
        ])
        
        return df
    
    def compute_statistics(self, df):
        """Compute statistical summaries and confidence intervals."""
        stats = df.groupby('implementation').agg({
            'test_mse': ['mean', 'std', 'min', 'max'],
            'test_r2': ['mean', 'std', 'min', 'max'],
            'execution_time': ['mean', 'std', 'min', 'max'],
            'peak_memory_mb': ['mean', 'std', 'min', 'max']
        }).round(6)
        
        # Compute 95% confidence intervals
        confidence_intervals = {}
        for impl in df['implementation'].unique():
            impl_data = df[df['implementation'] == impl]
            n = len(impl_data)
            
            confidence_intervals[impl] = {}
            for metric in ['test_mse', 'test_r2', 'execution_time', 'peak_memory_mb']:
                mean = impl_data[metric].mean()
                std = impl_data[metric].std()
                margin = 1.96 * std / np.sqrt(n)  # 95% CI
                confidence_intervals[impl][metric] = (mean - margin, mean + margin)
        
        return stats, confidence_intervals
    
    def generate_statistical_plots(self, df):
        """Generate statistical visualization plots."""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # Test MSE distribution
        sns.boxplot(data=df, x='implementation', y='test_mse', ax=axes[0,0])
        axes[0,0].set_title('Test MSE Distribution Across Iterations')
        axes[0,0].set_ylabel('Test MSE')
        
        # Test R² distribution
        sns.boxplot(data=df, x='implementation', y='test_r2', ax=axes[0,1])
        axes[0,1].set_title('Test R² Distribution Across Iterations')
        axes[0,1].set_ylabel('Test R²')
        
        # Execution time distribution
        sns.boxplot(data=df, x='implementation', y='execution_time', ax=axes[1,0])
        axes[1,0].set_title('Execution Time Distribution')
        axes[1,0].set_ylabel('Time (seconds)')
        axes[1,0].set_yscale('log')
        
        # Memory usage distribution
        sns.boxplot(data=df, x='implementation', y='peak_memory_mb', ax=axes[1,1])
        axes[1,1].set_title('Peak Memory Usage Distribution')
        axes[1,1].set_ylabel('Peak Memory (MB)')
        axes[1,1].set_yscale('log')
        
        plt.tight_layout()
        plt.savefig('../results/statistical_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("Statistical analysis plots saved to ../results/statistical_analysis.png")

def main():
    """Main statistical analysis function."""
    analyzer = StatisticalAnalyzer(n_iterations=10)
    
    # Run analysis
    df = analyzer.run_statistical_analysis()
    
    # Compute statistics
    stats, confidence_intervals = analyzer.compute_statistics(df)
    
    # Save results
    os.makedirs("../results", exist_ok=True)
    df.to_csv("../results/statistical_results.csv", index=False)
    stats.to_csv("../results/statistical_summary.csv")
    
    # Generate plots
    analyzer.generate_statistical_plots(df)
    
    # Print summary
    print("\n" + "="*80)
    print("STATISTICAL ANALYSIS SUMMARY")
    print("="*80)
    print(stats)
    
    print("\n95% Confidence Intervals:")
    for impl, intervals in confidence_intervals.items():
        print(f"\n{impl}:")
        for metric, (lower, upper) in intervals.items():
            print(f"  {metric}: [{lower:.6f}, {upper:.6f}]")
    
    print(f"\nResults saved to ../results/statistical_results.csv")
    print(f"Summary saved to ../results/statistical_summary.csv")

if __name__ == "__main__":
    main()
