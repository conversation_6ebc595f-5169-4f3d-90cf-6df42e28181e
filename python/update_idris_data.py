#!/usr/bin/env python3
"""
Update Idris LinearRegression.idr file with 100 samples instead of 50.
This script generates the data section and updates the file.
"""

import pandas as pd
import numpy as np

def generate_idris_data():
    # Load standardized training data
    train_df = pd.read_csv("../data/california_train_standardized.csv")
    
    # Take first 100 samples
    sample_df = train_df.head(100)
    
    # Extract features and targets
    feature_cols = [col for col in train_df.columns if col != 'target']
    X = sample_df[feature_cols].values
    y = sample_df['target'].values
    
    # Generate Idris matrix data
    print("  let standardizedInputs : Matrix SAMPLE_SIZE FEATURES = [")
    for i, row in enumerate(X):
        formatted_row = "[" + ", ".join(f"{val:.8f}" for val in row) + "]"
        if i < len(X) - 1:
            print(f"    {formatted_row},")
        else:
            print(f"    {formatted_row}")
    print("  ]")
    print()
    
    # Generate Idris vector data
    print("  let standardizedTargets : Vector SAMPLE_SIZE = [")
    target_str = ", ".join(f"{val:.8f}" for val in y)
    # Split into lines of reasonable length
    words = target_str.split(", ")
    lines = []
    current_line = "    "
    for word in words:
        if len(current_line + word + ", ") > 120:
            lines.append(current_line.rstrip(", "))
            current_line = "    " + word + ", "
        else:
            current_line += word + ", "
    if current_line.strip():
        lines.append(current_line.rstrip(", "))
    
    for i, line in enumerate(lines):
        if i < len(lines) - 1:
            print(line + ",")
        else:
            print(line)
    print("  ]")
    print()
    print("  pure $ Just (standardizedInputs, standardizedTargets)")

if __name__ == "__main__":
    generate_idris_data()
