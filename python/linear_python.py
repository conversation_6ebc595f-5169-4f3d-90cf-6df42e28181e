#!/usr/bin/env python3
"""
Python linear regression implementations for comparison with shape-safe Idris version.
Demonstrates runtime shape checking vs compile-time guarantees.
TODO: maybe refactor this later, getting a bit messy
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.metrics import mean_squared_error, r2_score
import time
import os
import warnings
import tracemalloc  # track peak memory without extra deps
from typing import Tuple, Optional, List, Union
from dataclasses import dataclass
import argparse
from unified_data_loader import load_standardized_data, verify_data_setup

# suppress annoying warnings
warnings.filterwarnings('ignore')

# Type hints for shape safety (limited compared to Idris)
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from nptyping import NDArray, Float64
    from typing_extensions import Literal
    
    # Attempt to use shape annotations (limited support)
    Matrix13x1 = NDArray[(13, 1), Float64]
    MatrixNx13 = NDArray[(None, 13), Float64]  # None = variable batch size
    VectorN = NDArray[(None,), Float64]

@dataclass
class LinearConfig:
    """Configuration for linear regression experiments."""
    learning_rate: float = 0.01
    max_iterations: int = 1000
    regularization: float = 0.001
    early_stopping: bool = True
    tolerance: float = 1e-6

@dataclass
class ExperimentResults:
    """Results from linear regression experiment."""
    weights: np.ndarray
    bias: float
    train_mse: float
    test_mse: float
    train_r2: float
    test_r2: float
    execution_time: float
    peak_memory_mb: float
    iterations_used: int
    shape_errors: List[str]

class ShapeSafeLinearRegression:
    """
    Manual linear regression with explicit shape checking.
    Demonstrates runtime verification vs Idris compile-time guarantees.

    Note: this is getting pretty complex, maybe should simplify later
    """

    def __init__(self, config: LinearConfig):
        self.config = config
        self.weights: Optional[np.ndarray] = None
        self.bias: Optional[float] = None
        self.fitted: bool = False
        self.loss_history: List[float] = []
        self.shape_errors: List[str] = []
        # debug: keep track of iterations for debugging
        self._debug_iter_count = 0

    def _verify_input_shape(self, X: np.ndarray, context: str = "input") -> bool:
        """Verify input has exactly 8 features (California Housing constraint)."""
        expected_features = 8  # hardcoded for now, should make configurable

        if X.ndim != 2:
            error = f"Expected 2D array for {context}, got {X.ndim}D"
            self.shape_errors.append(error)
            return False
            
        if X.shape[1] != expected_features:
            error = f"Expected {expected_features} features for {context}, got {X.shape[1]}"
            self.shape_errors.append(error)
            return False
            
        return True
        
    def _verify_weight_shape(self, weights: np.ndarray) -> bool:
        """Verify weights have shape [8, 1] for matrix multiplication."""
        expected_shape = (8, 1)
        
        if weights.shape != expected_shape:
            error = f"Expected weight shape {expected_shape}, got {weights.shape}"
            self.shape_errors.append(error)
            return False
            
        return True
        
    def _verify_target_shape(self, y: np.ndarray, n_samples: int) -> bool:
        """Verify target vector has correct shape."""
        if y.ndim != 1:
            error = f"Expected 1D target array, got {y.ndim}D"
            self.shape_errors.append(error)
            return False
            
        if len(y) != n_samples:
            error = f"Target length {len(y)} doesn't match input samples {n_samples}"
            self.shape_errors.append(error)
            return False
            
        return True
    
    def _matrix_multiply_safe(self, X: np.ndarray, weights: np.ndarray) -> np.ndarray:
        """Shape-safe matrix multiplication with explicit verification."""
        # Verify shapes before multiplication
        if X.shape[1] != weights.shape[0]:
            error = f"Matrix multiplication incompatible: {X.shape} @ {weights.shape}"
            self.shape_errors.append(error)
            raise ValueError(error)
            
        try:
            result = X @ weights
            
            # Verify output shape
            expected_output_shape = (X.shape[0], weights.shape[1])
            if result.shape != expected_output_shape:
                error = f"Unexpected output shape: {result.shape}, expected {expected_output_shape}"
                self.shape_errors.append(error)
                
            return result
            
        except ValueError as e:
            error = f"Matrix multiplication failed: {str(e)}"
            self.shape_errors.append(error)
            raise
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions with shape verification."""
        if not self.fitted:
            raise RuntimeError("Model must be fitted before prediction")
        
        # Runtime shape checking (vs Idris compile-time)
        if not self._verify_input_shape(X, "prediction input"):
            raise ValueError(f"Shape errors: {self.shape_errors}")
            
        # Shape-safe matrix multiplication
        linear_output = self._matrix_multiply_safe(X, self.weights)
        
        # Add bias (broadcasting)
        predictions = linear_output.flatten() + self.bias
        
        return predictions
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'ShapeSafeLinearRegression':
        """Fit linear regression with gradient descent and shape verification."""
        print(f"Fitting shape-safe linear regression")

        # Runtime shape verification (ugh, so much boilerplate compared to Idris)
        if not self._verify_input_shape(X, "training input"):
            raise ValueError(f"Input shape errors: {self.shape_errors}")

        if not self._verify_target_shape(y, X.shape[0]):
            raise ValueError(f"Target shape errors: {self.shape_errors}")

        # Initialize parameters with correct shapes (use different seed from other implementations)
        np.random.seed(456)  # Different seed to ensure independent initialization from other methods
        self.weights = np.random.normal(0, 0.01, size=(8, 1))  # hardcoded 8 features for California Housing
        self.bias = 0.0
        self.fitted = True  # Set fitted to True after initialization

        # debug: double check this works
        assert self.weights.shape == (8, 1), f"Weight shape wrong: {self.weights.shape}"

        # Verify weight initialization (probably overkill but whatever)
        if not self._verify_weight_shape(self.weights):
            raise ValueError(f"Weight initialization failed: {self.shape_errors}")

        print(f"   Input shape: {X.shape}")
        print(f"   Weight shape: {self.weights.shape}")
        print(f"   Expected output shape: {(X.shape[0], 1)}")

        n_samples = X.shape[0]
        
        # Gradient descent training (this is getting tedious...)
        for iteration in range(self.config.max_iterations):
            self._debug_iter_count += 1  # debug counter

            # Forward pass with shape verification
            predictions = self.predict(X)

            # Compute loss
            mse_loss = np.mean((predictions - y) ** 2)
            reg_loss = self.config.regularization * np.sum(self.weights ** 2)
            total_loss = mse_loss + reg_loss

            # Compute gradients with shape verification (so much manual work!)
            errors = predictions - y

            # Weight gradients: X.T @ errors, ensuring shape compatibility
            if X.T.shape[1] != len(errors):
                error = f"Gradient computation shape mismatch: {X.T.shape} vs errors {len(errors)}"
                self.shape_errors.append(error)
                raise ValueError(error)

            weight_gradients = (X.T @ errors.reshape(-1, 1)) / n_samples
            weight_gradients += self.config.regularization * self.weights  # L2 regularization

            bias_grad = np.mean(errors)  # shorter variable name

            # Update parameters
            self.weights -= self.config.learning_rate * weight_gradients
            self.bias -= self.config.learning_rate * bias_grad

            self.loss_history.append(total_loss)
            
            # Early stopping
            if (self.config.early_stopping and 
                len(self.loss_history) > 10 and 
                abs(self.loss_history[-1] - self.loss_history[-10]) < self.config.tolerance):
                print(f"   Early stopping at iteration {iteration}")
                break
                
            if iteration % 100 == 0:
                print(f"   Iteration {iteration}: loss = {total_loss:.6f}")
        
        self.fitted = True
        print(f"   Final weights shape: {self.weights.shape}")
        print(f"   Final bias: {self.bias}")
        
        return self
    
    def get_metrics(self, X: np.ndarray, y: np.ndarray) -> Tuple[float, float]:
        """Compute MSE and R² with shape verification."""
        predictions = self.predict(X)
        mse = mean_squared_error(y, predictions)
        r2 = r2_score(y, predictions)
        return mse, r2

class PyTorchLinearRegression(nn.Module):
    """PyTorch implementation for comparison."""
    
    def __init__(self):
        super().__init__()
        # Automatic shape inference, but no compile-time verification
        self.linear = nn.Linear(8, 1)  # Must manually ensure 8 features
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass - shape errors detected at runtime."""
        return self.linear(x)

def demonstrate_shape_errors():
    """Demonstrate shape mismatches that cause runtime errors in Python."""
    print("\n" + "="*60)
    print("SHAPE MISMATCH DEMONSTRATION")
    print("="*60)
    
    # Load sample data
    try:
        train_df = pd.read_csv("../data/california_train.csv")
        X_sample = train_df.iloc[:5, :-1].values  # 5 samples, 8 features
        print(f"Sample data shape: {X_sample.shape}")
        
        # Correct case
        W_correct = np.random.randn(8, 1)
        pred_correct = X_sample @ W_correct
        print(f"CORRECT: {X_sample.shape} @ {W_correct.shape} = {pred_correct.shape}")

        # Wrong number of features
        print(f"\nTesting shape mismatches (these will cause runtime errors):")

        try:
            W_wrong1 = np.random.randn(7, 1)  # Too few features
            pred_wrong1 = X_sample @ W_wrong1  # this should fail
            print(f"ERROR: Unexpected success: {X_sample.shape} @ {W_wrong1.shape}")
        except ValueError as e:
            print(f"Runtime error (7 features): {str(e)}")

        try:
            W_wrong2 = np.random.randn(9, 1)  # Too many features
            pred_wrong2 = X_sample @ W_wrong2  # this should also fail
            print(f"ERROR: Unexpected success: {X_sample.shape} @ {W_wrong2.shape}")
        except ValueError as e:
            print(f"Runtime error (9 features): {str(e)}")

        try:
            W_wrong3 = np.random.randn(8, 2)  # Wrong output dimension
            pred_wrong3 = X_sample @ W_wrong3
            print(f"WARNING: Wrong output shape: {X_sample.shape} @ {W_wrong3.shape} = {pred_wrong3.shape}")
            print(f"   Expected: (5, 1), Got: {pred_wrong3.shape}")
        except ValueError as e:
            print(f"Runtime error (wrong output): {str(e)}")

        print(f"\nIdris prevents ALL these errors at compile time!")
        
    except Exception as e:
        print(f" Demonstration failed: {e}")

class LinearRegressionExperiment:
    """Main experiment orchestrator."""
    
    def __init__(self, config: LinearConfig):
        self.config = config
        self.results = {}
        
    def load_dataset(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Load standardized California Housing dataset."""
        print(" Loading standardized California Housing dataset...")

        # Verify data setup first
        if not verify_data_setup(verbose=False):
            raise ValueError("Data setup verification failed. Please run: python create_standardized_dataset.py")

        # Load standardized data with verification
        X_train, X_test, y_train, y_test = load_standardized_data(
            verify_integrity=True,
            verify_consistency=True,
            verbose=True
        )

        print(f"Loaded standardized dataset: {X_train.shape} train, {X_test.shape} test")
        print(f"Data consistency verified with checksums")

        return X_train, y_train, X_test, y_test
    
    def run_manual_implementation(self, X_train: np.ndarray, y_train: np.ndarray,
                                 X_test: np.ndarray, y_test: np.ndarray) -> ExperimentResults:
        """Run manual shape-safe implementation."""
        print(f"\n Running Manual Shape-Safe Implementation")
        print("-" * 50)

        tracemalloc.start()
        start_time = time.time()

        # Create and fit model
        model = ShapeSafeLinearRegression(self.config)
        model.fit(X_train, y_train)

        # Evaluate
        train_mse, train_r2 = model.get_metrics(X_train, y_train)
        test_mse, test_r2 = model.get_metrics(X_test, y_test)

        end_time = time.time()
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        peak_mb = peak / (1024 * 1024)

        print(f"Results:")
        print(f"   Train MSE: {train_mse:.6f}, R²: {train_r2:.4f}")
        print(f"   Test MSE: {test_mse:.6f}, R²: {test_r2:.4f}")
        print(f"   Execution time: {end_time - start_time:.4f}s")
        print(f"   Peak memory: {peak_mb:.2f} MB")

        if model.shape_errors:
            print(f"⚠️  Shape errors detected:")
            for error in model.shape_errors:
                print(f"   - {error}")

        return ExperimentResults(
            weights=model.weights.flatten(),
            bias=model.bias,
            train_mse=train_mse,
            test_mse=test_mse,
            train_r2=train_r2,
            test_r2=test_r2,
            execution_time=end_time - start_time,
            peak_memory_mb=peak_mb,
            iterations_used=len(model.loss_history),
            shape_errors=model.shape_errors
        )
    
    def run_pytorch_implementation(self, X_train: np.ndarray, y_train: np.ndarray,
                                  X_test: np.ndarray, y_test: np.ndarray) -> ExperimentResults:
        """Run PyTorch implementation."""
        print(f"\n Running PyTorch Implementation")
        print("-" * 50)
        
        tracemalloc.start()
        start_time = time.time()

        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train)
        y_train_tensor = torch.FloatTensor(y_train).unsqueeze(1)
        X_test_tensor = torch.FloatTensor(X_test)

        # Create model and optimizer
        model = PyTorchLinearRegression()
        optimizer = optim.SGD(model.parameters(), lr=self.config.learning_rate)
        criterion = nn.MSELoss()

        shape_errors = []

        # Training loop
        for epoch in range(self.config.max_iterations):
            optimizer.zero_grad()

            try:
                predictions = model(X_train_tensor)
                loss = criterion(predictions, y_train_tensor)

                # Add regularization
                l2_loss = self.config.regularization * sum(p.pow(2.0).sum() for p in model.parameters())
                total_loss = loss + l2_loss

                total_loss.backward()
                optimizer.step()

            except RuntimeError as e:
                if "size mismatch" in str(e).lower():
                    shape_errors.append(f"PyTorch shape error at epoch {epoch}: {str(e)}")
                    break
                else:
                    raise

            if epoch % 100 == 0:
                print(f"   Epoch {epoch}: loss = {total_loss.item():.6f}")

        # Evaluate
        model.eval()
        with torch.no_grad():
            train_pred = model(X_train_tensor).numpy().flatten()
            test_pred = model(X_test_tensor).numpy().flatten()

        train_mse = mean_squared_error(y_train, train_pred)
        test_mse = mean_squared_error(y_test, test_pred)
        train_r2 = r2_score(y_train, train_pred)
        test_r2 = r2_score(y_test, test_pred)

        end_time = time.time()
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        peak_mb = peak / (1024 * 1024)

        # Extract weights and bias
        weights = model.linear.weight.detach().numpy().flatten()
        bias = model.linear.bias.detach().numpy().item()

        print(f"Results:")
        print(f"   Train MSE: {train_mse:.6f}, R²: {train_r2:.4f}")
        print(f"   Test MSE: {test_mse:.6f}, R²: {test_r2:.4f}")
        print(f"   Execution time: {end_time - start_time:.4f}s")
        print(f"   Peak memory: {peak_mb:.2f} MB")

        return ExperimentResults(
            weights=weights,
            bias=bias,
            train_mse=train_mse,
            test_mse=test_mse,
            train_r2=train_r2,
            test_r2=test_r2,
            execution_time=end_time - start_time,
            peak_memory_mb=peak_mb,
            iterations_used=self.config.max_iterations,
            shape_errors=shape_errors
        )
    
    def run_sklearn_implementation(self, X_train: np.ndarray, y_train: np.ndarray,
                                  X_test: np.ndarray, y_test: np.ndarray) -> ExperimentResults:
        """Run scikit-learn implementation."""
        print(f"\n Running Scikit-Learn Implementation")
        print("-" * 50)
        
        tracemalloc.start()
        start_time = time.time()

        # Create model
        if self.config.regularization > 0:
            model = Ridge(alpha=self.config.regularization)
        else:
            model = LinearRegression()

        shape_errors = []

        try:
            # Fit model
            model.fit(X_train, y_train)

            # Make predictions
            train_pred = model.predict(X_train)
            test_pred = model.predict(X_test)

        except ValueError as e:
            if "shape" in str(e).lower():
                shape_errors.append(f"Sklearn shape error: {str(e)}")
                raise
            else:
                raise

        # Compute metrics
        train_mse = mean_squared_error(y_train, train_pred)
        test_mse = mean_squared_error(y_test, test_pred)
        train_r2 = r2_score(y_train, train_pred)
        test_r2 = r2_score(y_test, test_pred)

        end_time = time.time()
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        peak_mb = peak / (1024 * 1024)

        print(f"Results:")
        print(f"   Train MSE: {train_mse:.6f}, R²: {train_r2:.4f}")
        print(f"   Test MSE: {test_mse:.6f}, R²: {test_r2:.4f}")
        print(f"   Execution time: {end_time - start_time:.4f}s")
        print(f"   Peak memory: {peak_mb:.2f} MB")

        return ExperimentResults(
            weights=model.coef_,
            bias=model.intercept_,
            train_mse=train_mse,
            test_mse=test_mse,
            train_r2=train_r2,
            test_r2=test_r2,
            execution_time=end_time - start_time,
            peak_memory_mb=peak_mb,
            iterations_used=1,  # Direct solve
            shape_errors=shape_errors
        )
    
    def save_results(self):
        """Save experiment results."""
        os.makedirs("../results", exist_ok=True)
        
        # Save predictions and weights from each method
        for method_name, result in self.results.items():
            np.savetxt(f"../results/python_{method_name}_weights.txt", result.weights)
            with open(f"../results/python_{method_name}_bias.txt", "w") as f:
                f.write(str(result.bias))
        
        # Save summary report
        with open("../results/python_linear_results.txt", "w") as f:
            f.write("Linear Regression Results - California Housing\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"Configuration:\n")
            f.write(f"  Learning rate: {self.config.learning_rate}\n")
            f.write(f"  Max iterations: {self.config.max_iterations}\n")
            f.write(f"  Regularization: {self.config.regularization}\n\n")

            for method_name, result in self.results.items():
                f.write(f"{method_name.title()} Implementation:\n")
                f.write(f"  Train MSE: {result.train_mse:.6f}\n")
                f.write(f"  Test MSE: {result.test_mse:.6f}\n")
                f.write(f"  Train R²: {result.train_r2:.4f}\n")
                f.write(f"  Test R²: {result.test_r2:.4f}\n")
                f.write(f"  Execution time: {result.execution_time:.4f}s\n")
                f.write(f"  Peak memory: {getattr(result, 'peak_memory_mb', float('nan')):.2f} MB\n")
                f.write(f"  Shape errors: {len(result.shape_errors)}\n")
                if result.shape_errors:
                    for error in result.shape_errors:
                        f.write(f"    - {error}\n")
                f.write("\n")

        # Also save a CSV summary for easy table generation
        import csv
        csv_path = "../results/python_linear_results.csv"
        with open(csv_path, "w", newline="") as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(["method", "train_mse", "test_mse", "train_r2", "test_r2", "time_s", "peak_mem_mb", "iterations", "shape_error_count"])
            for method_name, result in self.results.items():
                writer.writerow([
                    method_name,
                    f"{result.train_mse:.6f}",
                    f"{result.test_mse:.6f}",
                    f"{result.train_r2:.4f}",
                    f"{result.test_r2:.4f}",
                    f"{result.execution_time:.4f}",
                    f"{getattr(result, 'peak_memory_mb', float('nan')):.2f}",
                    getattr(result, 'iterations_used', ''),
                    len(result.shape_errors),
                ])

        print("Results saved to ../results/")
    
    def compare_implementations(self):
        """Compare results across implementations."""
        print("\n" + "="*70)
        print("IMPLEMENTATION COMPARISON")
        print("="*70)
        
        print(f"{'Method':<12} {'Train MSE':<12} {'Test MSE':<12} {'Test R²':<10} {'Time(s)':<8} {'Peak MB':<8} {'Shape Errors'}")
        print("-" * 82)

        for method_name, result in self.results.items():
            shape_error_count = len(result.shape_errors)
            print(f"{method_name.title():<12} {result.train_mse:<12.6f} {result.test_mse:<12.6f} "
                  f"{result.test_r2:<10.4f} {result.execution_time:<8.4f} {getattr(result, 'peak_memory_mb', float('nan')):<8.2f} {shape_error_count}")
        
        print(f"\nShape Safety Comparison:")
        print(f"  - Idris: COMPILE-TIME verification - errors impossible")
        print(f"  - Python: RUNTIME verification - errors detected during execution")
        
    def run_full_experiment(self):
        """Run complete linear regression experiment."""
        print("Shape-Safe Linear Regression Experiment - California Housing")
        print("=" * 70)
        
        # Load data
        X_train, y_train, X_test, y_test = self.load_dataset()
        
        # Run all implementations
        self.results['manual'] = self.run_manual_implementation(X_train, y_train, X_test, y_test)
        self.results['pytorch'] = self.run_pytorch_implementation(X_train, y_train, X_test, y_test)
        self.results['sklearn'] = self.run_sklearn_implementation(X_train, y_train, X_test, y_test)
        
        # Compare results
        self.compare_implementations()
        
        # Save results
        self.save_results()
        
        # Demonstrate shape errors
        demonstrate_shape_errors()
        
        print("\nPython linear regression experiments completed!")

def main():
    parser = argparse.ArgumentParser(description="Python linear regression experiment")
    parser.add_argument("--lr", type=float, default=0.01, help="Learning rate")
    parser.add_argument("--iterations", type=int, default=1000, help="Max iterations")
    parser.add_argument("--regularization", type=float, default=0.001, help="L2 regularization")
    parser.add_argument("--demo-shapes", action="store_true", help="Run shape mismatch demonstration")
    
    args = parser.parse_args()
    
    config = LinearConfig(
        learning_rate=args.lr,
        max_iterations=args.iterations,
        regularization=args.regularization
    )
    
    if args.demo_shapes:
        demonstrate_shape_errors()
        return
    
    experiment = LinearRegressionExperiment(config)
    experiment.run_full_experiment()

if __name__ == "__main__":
    main()