#!/usr/bin/env python3
"""
Boston Housing data processing for linear regression shape safety experiment.
Downloads, processes, and saves Boston Housing dataset with proper feature scaling.

TODO: this is getting pretty messy, should refactor at some point
"""

import numpy as np
import pandas as pd
import os
from sklearn.datasets import fetch_california_housing
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import argparse
import warnings

# suppress sklearn deprecation warning for Boston Housing (annoying)
warnings.filterwarnings("ignore", category=FutureWarning)
# also suppress other warnings for cleaner output
warnings.filterwarnings("ignore")

def create_data_directory():
    """Create data directory if it doesn't exist."""
    os.makedirs("../data", exist_ok=True)
    print("Data directory created/verified")

def load_and_process_california():
    """Load and preprocess the California Housing dataset.

    Note: using California housing instead of Boston (deprecated)
    """
    print("Loading California Housing dataset...")

    # Load California Housing dataset
    california = fetch_california_housing()
    X, y = california.data, california.target
    feature_names = california.feature_names
    
    print("Loaded California Housing dataset")
    print("   Modern, ethically-sound housing price prediction dataset")

    print(f"Dataset shape: {X.shape} -> {y.shape}")
    print(f"Features ({len(feature_names)}): {list(feature_names)}")

    # Verify we have exactly 8 features (California Housing standard)
    assert X.shape[1] == 8, f"Expected 8 features, got {X.shape[1]}"  # hardcoded for now
    
    return X, y, feature_names

def split_and_scale_data(X, y, test_size=0.3, random_state=42):
    """Split data into train/test sets and standardize features."""
    print(f"🔀 Splitting data: {100*(1-test_size):.0f}% train, {100*test_size:.0f}% test")
    
    # Split the data  
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state
    )
    
    # Standardize features (critical for linear regression)
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    print(f"Data split: {len(X_train)} train, {len(X_test)} test samples")
    print(f"Features standardized (mean≈0, std≈1)")
    
    return X_train_scaled, X_test_scaled, y_train, y_test, scaler

def save_data(X_train, X_test, y_train, y_test, X_full, y_full, feature_names):
    """Save processed data to CSV files for both Python and Idris."""
    
    # Create DataFrames with proper column names
    train_df = pd.DataFrame(X_train, columns=feature_names)
    train_df['target'] = y_train
    
    test_df = pd.DataFrame(X_test, columns=feature_names)
    test_df['target'] = y_test
    
    full_df = pd.DataFrame(X_full, columns=feature_names)
    full_df['target'] = y_full
    
    # Save to CSV files with high precision for numerical accuracy
    train_df.to_csv("../data/california_train.csv", index=False, float_format='%.8f')
    test_df.to_csv("../data/california_test.csv", index=False, float_format='%.8f')
    full_df.to_csv("../data/california_full.csv", index=False, float_format='%.8f')
    
    print(f"Saved training data: ../data/california_train.csv ({len(train_df)} samples)")
    print(f"Saved test data: ../data/california_test.csv ({len(test_df)} samples)")
    print(f"Saved full data: ../data/california_full.csv ({len(full_df)} samples)")

def print_data_statistics(X_train, X_test, y_train, y_test, feature_names):
    """Print comprehensive dataset statistics."""
    print("\n" + "="*60)
    print("CALIFORNIA HOUSING DATASET STATISTICS")
    print("="*60)
    
    print(f"Training set: {X_train.shape}")
    print(f"Test set: {X_test.shape}")
    print(f"Features: {X_train.shape[1]} (California Housing standard)")
    print(f"Task: Regression (continuous target)")
    
    print(f"\nTarget variable statistics:")
    print(f"  Train - Mean: {y_train.mean():.2f}, Std: {y_train.std():.2f}")
    print(f"  Train - Range: [{y_train.min():.2f}, {y_train.max():.2f}]")
    print(f"  Test  - Mean: {y_test.mean():.2f}, Std: {y_test.std():.2f}")
    print(f"  Test  - Range: [{y_test.min():.2f}, {y_test.max():.2f}]")
    
    print(f"\nFeature statistics (training set after scaling):")
    print(f"  Mean: {X_train.mean(axis=0)}")
    print(f"  Std:  {X_train.std(axis=0)}")
    
    print(f"\nFeature names:")
    for i, name in enumerate(feature_names):
        print(f"  {i:2d}: {name}")
        
    print(f"\n Shape Safety Constraints:")
    print(f"  Input shape:  [batch_size, 8] - exactly 8 features required")
    print(f"  Weight shape: [8, 1] - matches input feature count")
    print(f"  Output shape: [batch_size, 1] - single continuous prediction")

def verify_shape_consistency():
    """Verify that the dataset meets shape safety requirements."""
    print("\n" + "="*60)
    print("SHAPE SAFETY VERIFICATION")
    print("="*60)
    
    try:
        # Load saved data to verify shapes
        train_df = pd.read_csv("../data/california_train.csv")
        test_df = pd.read_csv("../data/california_test.csv")
        
        # Extract features and target
        X_train = train_df.iloc[:, :-1].values
        y_train = train_df.iloc[:, -1].values
        X_test = test_df.iloc[:, :-1].values
        y_test = test_df.iloc[:, -1].values
        
        print(f"Training features shape: {X_train.shape}")
        print(f"Training targets shape:  {y_train.shape}")
        print(f"Test features shape:     {X_test.shape}")
        print(f"Test targets shape:      {y_test.shape}")

        # Verify 8 features constraint (hardcoded assumption)
        assert X_train.shape[1] == 8, f"Expected 8 features, got {X_train.shape[1]}"
        assert X_test.shape[1] == 8, f"Expected 8 features, got {X_test.shape[1]}"

        print(f"Shape safety verified: Exactly 8 features as required")

        # Demonstrate what would happen with wrong shapes
        print(f"\nShape mismatches that would cause runtime errors in Python:")
        print(f"   - Input [m, 7] @ Weight [8, 1] -> Runtime Error")
        print(f"   - Input [m, 8] @ Weight [9, 1] -> Runtime Error")
        print(f"   - Input [m, 8] @ Weight [8, 2] -> Wrong output shape")

        print(f"\nIdris prevents these errors at compile time!")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Shape verification failed: {e}")
        return False

def create_shape_mismatch_examples():
    """Create examples that demonstrate shape safety importance."""
    print("\n" + "="*60)
    print("SHAPE SAFETY DEMONSTRATION")
    print("="*60)
    
    # Load a small sample to demonstrate
    train_df = pd.read_csv("../data/california_train.csv")
    X_sample = train_df.iloc[:5, :-1].values  # 5 samples, 8 features
    
    print(f"Sample data shape: {X_sample.shape}")
    
    # Correct matrix multiplication
    W_correct = np.random.randn(8, 1)  # Correct weight shape
    predictions_correct = X_sample @ W_correct
    print(f"CORRECT: {X_sample.shape} @ {W_correct.shape} = {predictions_correct.shape}")

    # Demonstrate what fails in Python (runtime errors)
    try:
        W_wrong = np.random.randn(7, 1)  # Wrong number of features
        predictions_wrong = X_sample @ W_wrong  # this should fail
        print(f"ERROR: This should have failed but didn't!")
    except ValueError as e:
        print(f"Python runtime error: {str(e)[:60]}...")

    try:
        W_wrong2 = np.random.randn(9, 1)  # Too many features
        predictions_wrong2 = X_sample @ W_wrong2  # this should also fail
        print(f"ERROR: This should have failed but didn't!")
    except ValueError as e:
        print(f"Python runtime error: {str(e)[:60]}...")

    print(f"\nIdris guarantee: Such errors are impossible at compile time!")

def main():
    parser = argparse.ArgumentParser(description="Process California Housing dataset for shape-safe linear regression")
    parser.add_argument("--test-size", type=float, default=0.3, 
                       help="Proportion of data for testing (default: 0.3)")
    parser.add_argument("--random-seed", type=int, default=42,
                       help="Random seed for reproducibility (default: 42)")
    parser.add_argument("--verify-shapes", action="store_true",
                       help="Run shape safety verification tests")
    args = parser.parse_args()
    
    print("California Housing Dataset Processing for Shape-Safe Linear Regression")
    print("="*75)
    
    # Create data directory
    create_data_directory()
    
    # Load and process data
    X, y, feature_names = load_and_process_california()
    
    # Split and scale the data
    X_train, X_test, y_train, y_test, scaler = split_and_scale_data(
        X, y, test_size=args.test_size, random_state=args.random_seed
    )
    
    # Prepare full scaled dataset
    X_full_scaled = scaler.fit_transform(X)
    
    # Save all data
    save_data(X_train, X_test, y_train, y_test, X_full_scaled, y, feature_names)
    
    # Print statistics
    print_data_statistics(X_train, X_test, y_train, y_test, feature_names)
    
    # Verify shape consistency
    if verify_shape_consistency():
        print(f"\nData processing complete! Files saved to ../data/")
        print("Ready for shape-safe linear regression experiments")

        if args.verify_shapes:
            create_shape_mismatch_examples()
    else:
        print(f"\nERROR: Data processing completed with shape verification issues")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())