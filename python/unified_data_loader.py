#!/usr/bin/env python3
"""
Unified data loading and validation module for consistent linear regression experiments.
Ensures all implementations use identical data with proper validation.
"""

import numpy as np
import pandas as pd
import os
import hashlib
import json
from typing import Tuple, Dict, Optional
from dataclasses import dataclass
import warnings

@dataclass
class DatasetInfo:
    """Information about the loaded dataset."""
    train_samples: int
    test_samples: int
    total_samples: int
    features: int
    feature_names: list
    target_name: str
    train_shape: tuple
    test_shape: tuple
    preprocessing_applied: bool
    checksums_verified: bool

class UnifiedDataLoader:
    """
    Unified data loader that ensures all implementations use identical data.
    Provides validation, checksum verification, and consistent preprocessing.
    """

    def __init__(self, data_dir: str = "../data"):
        self.data_dir = data_dir
        self.train_path = os.path.join(data_dir, "california_train_standardized.csv")
        self.test_path = os.path.join(data_dir, "california_test_standardized.csv")
        self.full_path = os.path.join(data_dir, "california_full_standardized.csv")
        self.metadata_path = os.path.join(data_dir, "preprocessing_metadata.json")
        self.checksums_path = os.path.join(data_dir, "data_checksums.json")

        self.metadata: Optional[Dict] = None
        self.checksums: Optional[Dict] = None
        self.dataset_info: Optional[DatasetInfo] = None

    def _load_metadata(self) -> Dict:
        """Load preprocessing metadata."""
        if not os.path.exists(self.metadata_path):
            raise FileNotFoundError(f"Metadata file not found: {self.metadata_path}")

        with open(self.metadata_path, 'r') as f:
            metadata = json.load(f)

        return metadata

    def _load_checksums(self) -> Dict:
        """Load data checksums."""
        if not os.path.exists(self.checksums_path):
            raise FileNotFoundError(f"Checksums file not found: {self.checksums_path}")

        with open(self.checksums_path, 'r') as f:
            checksums = json.load(f)

        return checksums

    def _compute_file_checksum(self, filepath: str) -> str:
        """Compute SHA-256 checksum of a file."""
        sha256_hash = hashlib.sha256()
        with open(filepath, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()

    def verify_data_integrity(self, verbose: bool = True) -> bool:
        """Verify data integrity using checksums."""
        if verbose:
            print(" Verifying data integrity with checksums...")

        try:
            expected_checksums = self._load_checksums()

            files_to_check = [
                (self.train_path, "california_train_standardized.csv"),
                (self.test_path, "california_test_standardized.csv"),
                (self.full_path, "california_full_standardized.csv"),
                (self.metadata_path, "preprocessing_metadata.json")
            ]

            for filepath, filename in files_to_check:
                if not os.path.exists(filepath):
                    if verbose:
                        print(f" File not found: {filepath}")
                    return False

                actual_checksum = self._compute_file_checksum(filepath)
                expected_checksum = expected_checksums.get(filename)

                if actual_checksum != expected_checksum:
                    if verbose:
                        print(f" Checksum mismatch for {filename}")
                        print(f"   Expected: {expected_checksum}")
                        print(f"   Actual:   {actual_checksum}")
                    return False

                if verbose:
                    print(f" {filename}: checksum verified")

            if verbose:
                print(" All data integrity checks passed!")
            return True

        except Exception as e:
            if verbose:
                print(f" Data integrity verification failed: {e}")
            return False

    def verify_data_consistency(self, X_train: np.ndarray, X_test: np.ndarray,
                               y_train: np.ndarray, y_test: np.ndarray, verbose: bool = True) -> bool:
        """Verify loaded data meets consistency requirements."""
        if verbose:
            print(" Verifying data consistency...")

        try:
            metadata = self._load_metadata()
            config = metadata["dataset_config"]

            # Verify shapes
            expected_features = config["features"]
            if X_train.shape[1] != expected_features:
                if verbose:
                    print(f" Train features: expected {expected_features}, got {X_train.shape[1]}")
                return False

            if X_test.shape[1] != expected_features:
                if verbose:
                    print(f" Test features: expected {expected_features}, got {X_test.shape[1]}")
                return False

            # Verify sample counts
            expected_train = metadata["train_samples"]
            expected_test = metadata["test_samples"]

            if len(X_train) != expected_train:
                if verbose:
                    print(f" Train samples: expected {expected_train}, got {len(X_train)}")
                return False

            if len(X_test) != expected_test:
                if verbose:
                    print(f" Test samples: expected {expected_test}, got {len(X_test)}")
                return False

            # Verify standardization (training data should have mean≈0, std≈1)
            train_means = X_train.mean(axis=0)
            train_stds = X_train.std(axis=0)

            if not np.allclose(train_means, 0, atol=1e-8):
                if verbose:
                    print(f" Train means not zero: {train_means}")
                return False

            if not np.allclose(train_stds, 1, atol=1e-8):
                if verbose:
                    print(f" Train stds not one: {train_stds}")
                return False

            if verbose:
                print(" Shape verification passed")
                print(" Sample count verification passed")
                print(" Standardization verification passed")
                print(" All data consistency checks passed!")

            return True

        except Exception as e:
            if verbose:
                print(f" Data consistency verification failed: {e}")
            return False

    def load_train_test_data(self, verify_integrity: bool = True,
                            verify_consistency: bool = True,
                            verbose: bool = True) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        Load training and test data with optional verification.

        Returns:
            Tuple of (X_train, X_test, y_train, y_test)
        """
        if verbose:
            print(" Loading standardized California Housing data...")

        # Verify data integrity first
        if verify_integrity:
            if not self.verify_data_integrity(verbose=verbose):
                raise ValueError("Data integrity verification failed")

        # Load metadata
        self.metadata = self._load_metadata()
        config = self.metadata["dataset_config"]

        # Load training data
        if not os.path.exists(self.train_path):
            raise FileNotFoundError(f"Training data not found: {self.train_path}")

        train_df = pd.read_csv(self.train_path)

        # Load test data
        if not os.path.exists(self.test_path):
            raise FileNotFoundError(f"Test data not found: {self.test_path}")

        test_df = pd.read_csv(self.test_path)

        # Extract features and targets
        target_name = config["target_name"]
        feature_names = self.metadata["feature_names"]

        X_train = train_df[feature_names].values
        y_train = train_df[target_name].values
        X_test = test_df[feature_names].values
        y_test = test_df[target_name].values

        if verbose:
            print(f" Loaded training data: {X_train.shape} -> {y_train.shape}")
            print(f" Loaded test data: {X_test.shape} -> {y_test.shape}")

        # Verify data consistency
        if verify_consistency:
            if not self.verify_data_consistency(X_train, X_test, y_train, y_test, verbose=verbose):
                raise ValueError("Data consistency verification failed")

        # Store dataset info
        self.dataset_info = DatasetInfo(
            train_samples=len(X_train),
            test_samples=len(X_test),
            total_samples=len(X_train) + len(X_test),
            features=X_train.shape[1],
            feature_names=feature_names,
            target_name=target_name,
            train_shape=X_train.shape,
            test_shape=X_test.shape,
            preprocessing_applied=True,
            checksums_verified=verify_integrity
        )

        return X_train, X_test, y_train, y_test

    def load_full_data(self, verify_integrity: bool = True, verbose: bool = True) -> Tuple[np.ndarray, np.ndarray]:
        """
        Load full dataset (for cases where train/test split is not needed).

        Returns:
            Tuple of (X_full, y_full)
        """
        if verbose:
            print(" Loading full standardized California Housing data...")

        # Verify data integrity first
        if verify_integrity:
            if not self.verify_data_integrity(verbose=verbose):
                raise ValueError("Data integrity verification failed")

        # Load metadata
        if self.metadata is None:
            self.metadata = self._load_metadata()

        config = self.metadata["dataset_config"]

        # Load full data
        if not os.path.exists(self.full_path):
            raise FileNotFoundError(f"Full data not found: {self.full_path}")

        full_df = pd.read_csv(self.full_path)

        # Extract features and targets
        target_name = config["target_name"]
        feature_names = self.metadata["feature_names"]

        X_full = full_df[feature_names].values
        y_full = full_df[target_name].values

        if verbose:
            print(f" Loaded full data: {X_full.shape} -> {y_full.shape}")

        return X_full, y_full

    def get_dataset_info(self) -> DatasetInfo:
        """Get information about the loaded dataset."""
        if self.dataset_info is None:
            raise ValueError("No dataset loaded yet. Call load_train_test_data() first.")
        return self.dataset_info

    def get_preprocessing_info(self) -> Dict:
        """Get preprocessing information (scaler parameters, etc.)."""
        if self.metadata is None:
            self.metadata = self._load_metadata()

        return {
            "scaler_mean": self.metadata["scaler_mean"],
            "scaler_scale": self.metadata["scaler_scale"],
            "feature_names": self.metadata["feature_names"],
            "dataset_config": self.metadata["dataset_config"]
        }

    def print_dataset_summary(self):
        """Print comprehensive dataset summary."""
        if self.metadata is None:
            self.metadata = self._load_metadata()

        config = self.metadata["dataset_config"]

        print("\n" + "="*80)
        print("STANDARDIZED DATASET SUMMARY")
        print("="*80)

        print(f" Dataset Configuration:")
        print(f"   Total samples: {config['subset_size']}")
        print(f"   Train samples: {self.metadata['train_samples']}")
        print(f"   Test samples: {self.metadata['test_samples']}")
        print(f"   Features: {config['features']}")
        print(f"   Random seed: {config['random_state']}")
        print(f"   Test split: {config['test_size']:.1%}")

        print(f"\n Preprocessing Applied:")
        print(f"   StandardScaler fitted on training data")
        print(f"   Features standardized: mean≈0, std≈1")
        print(f"   Stratified sampling maintained target distribution")

        print(f"\n Features:")
        for i, name in enumerate(self.metadata["feature_names"]):
            print(f"   {i:2d}: {name}")

        print(f"\n Shape Safety Constraints:")
        print(f"   Input shape:  [batch_size, {config['features']}]")
        print(f"   Weight shape: [{config['features']}, 1]")
        print(f"   Output shape: [batch_size, 1]")

def create_data_loader(data_dir: str = "../data") -> UnifiedDataLoader:
    """Factory function to create a UnifiedDataLoader instance."""
    return UnifiedDataLoader(data_dir)

# Convenience functions for quick data loading
def load_standardized_data(verify_integrity: bool = True,
                          verify_consistency: bool = True,
                          verbose: bool = True) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    Convenience function to quickly load standardized train/test data.

    Returns:
        Tuple of (X_train, X_test, y_train, y_test)
    """
    loader = UnifiedDataLoader()
    return loader.load_train_test_data(verify_integrity, verify_consistency, verbose)

def verify_data_setup(verbose: bool = True) -> bool:
    """
    Convenience function to verify the data setup is correct.

    Returns:
        True if all verification passes, False otherwise
    """
    try:
        loader = UnifiedDataLoader()

        # Check if files exist
        required_files = [
            loader.train_path,
            loader.test_path,
            loader.full_path,
            loader.metadata_path,
            loader.checksums_path
        ]

        for filepath in required_files:
            if not os.path.exists(filepath):
                if verbose:
                    print(f" Required file missing: {filepath}")
                return False

        # Verify integrity
        if not loader.verify_data_integrity(verbose=verbose):
            return False

        # Load and verify consistency
        X_train, X_test, y_train, y_test = loader.load_train_test_data(
            verify_integrity=False,  # Already verified above
            verify_consistency=True,
            verbose=verbose
        )

        if verbose:
            print(" All data setup verification passed!")
            loader.print_dataset_summary()

        return True

    except Exception as e:
        if verbose:
            print(f" Data setup verification failed: {e}")
        return False

if __name__ == "__main__":
    # Test the data loader
    print(" Testing Unified Data Loader")
    print("=" * 50)

    if verify_data_setup():
        print("\n Data loader test passed!")
    else:
        print("\n Data loader test failed!")
        exit(1)