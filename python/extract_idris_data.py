#!/usr/bin/env python3
"""
Extract first 50 samples from standardized data and format for Idris.

TODO: this is a quick hack, should make it more configurable
"""

import pandas as pd
import numpy as np
import warnings

# suppress pandas warnings
warnings.filterwarnings("ignore")

def extract_idris_data():
    # Load standardized training data (hopefully it exists)
    train_df = pd.read_csv("../data/california_train_standardized.csv")

    # Take first 100 samples for better comparison (increased from 50)
    sample_df = train_df.head(100)

    # Extract features and targets
    feature_cols = [col for col in train_df.columns if col != 'target']
    X = sample_df[feature_cols].values
    y = sample_df['target'].values

    print("-- Idris data extracted from standardized California Housing dataset")
    print("-- First 100 samples from training set (same preprocessing as Python)")
    print()

    # Format features for Idris
    print("let standardizedInputs : Matrix SAMPLE_SIZE FEATURES = [")
    for i, row in enumerate(X):
        formatted_row = "[" + ", ".join(f"{val:.8f}" for val in row) + "]"
        if i < len(X) - 1:
            print(f"    {formatted_row},")
        else:
            print(f"    {formatted_row}")
    print("  ]")
    print()

    # Format targets for Idris
    print("let standardizedTargets : Vector SAMPLE_SIZE = [")
    formatted_targets = []
    for i in range(0, len(y), 10):  # 10 per line
        line_targets = y[i:i+10]
        formatted_line = ", ".join(f"{val:.8f}" for val in line_targets)
        formatted_targets.append(f"    {formatted_line}")

    print(",\n".join(formatted_targets))
    print("  ]")

    print(f"\n-- Verification: {len(X)} samples, {X.shape[1]} features")
    print(f"-- Target range: [{y.min():.3f}, {y.max():.3f}]")
    print(f"-- Feature means: {X.mean(axis=0)}")
    print(f"-- Feature stds: {X.std(axis=0)}")

if __name__ == "__main__":
    extract_idris_data()