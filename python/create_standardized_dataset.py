#!/usr/bin/env python3
"""
Create standardized California Housing subset for fair linear regression comparison.
Generates a 1000-sample subset with proper stratification and consistent preprocessing.

TODO: this script is getting complex, might need refactoring
"""

import numpy as np
import pandas as pd
import os
import hashlib
from sklearn.datasets import fetch_california_housing
from sklearn.model_selection import train_test_split, StratifiedShuffleSplit
from sklearn.preprocessing import StandardScaler
from typing import <PERSON>ple, Dict
import json
import argparse
import warnings

# suppress warnings for cleaner output
warnings.filterwarnings("ignore")

# Configuration for standardized dataset
DATASET_CONFIG = {
    "subset_size": 1000,
    "test_size": 0.3,
    "random_state": 42,
    "stratify_bins": 5,  # For stratified sampling based on target values
    "features": 8,
    "target_name": "target"
}

def create_data_directory():
    """Create data directory if it doesn't exist."""
    os.makedirs("../data", exist_ok=True)
    print("Data directory created/verified")

def load_full_california_housing() -> Tuple[np.ndarray, np.ndarray, list]:
    """Load the full California Housing dataset."""
    print("Loading full California Housing dataset...")

    california = fetch_california_housing()
    X, y = california.data, california.target
    feature_names = california.feature_names

    print(f"Loaded full dataset: {X.shape} -> {y.shape}")
    print(f"Features: {list(feature_names)}")

    # Verify we have exactly 8 features
    assert X.shape[1] == 8, f"Expected 8 features, got {X.shape[1]}"

    return X, y, feature_names

def create_stratified_subset(X: np.ndarray, y: np.ndarray,
                           subset_size: int, random_state: int) -> Tuple[np.ndarray, np.ndarray]:
    """Create a stratified subset that maintains the distribution of target values."""
    print(f" Creating stratified subset of {subset_size} samples...")

    # Create bins for stratification based on target values
    y_binned = pd.cut(y, bins=DATASET_CONFIG["stratify_bins"], labels=False)

    # Use stratified sampling to maintain target distribution
    sss = StratifiedShuffleSplit(n_splits=1, train_size=subset_size,
                                random_state=random_state)

    subset_idx, _ = next(sss.split(X, y_binned))

    X_subset = X[subset_idx]
    y_subset = y[subset_idx]

    print(f"Created subset: {X_subset.shape} -> {y_subset.shape}")

    # Verify statistical properties are maintained (sanity check)
    print(f"Original target stats: mean={y.mean():.3f}, std={y.std():.3f}")
    print(f"Subset target stats:   mean={y_subset.mean():.3f}, std={y_subset.std():.3f}")

    return X_subset, y_subset

def split_and_standardize_data(X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, StandardScaler]:
    """Split data and apply standardization."""
    print(f"🔀 Splitting data: {100*(1-DATASET_CONFIG['test_size']):.0f}% train, {100*DATASET_CONFIG['test_size']:.0f}% test")

    # Split the data with fixed random state for reproducibility
    X_train, X_test, y_train, y_test = train_test_split(
        X, y,
        test_size=DATASET_CONFIG["test_size"],
        random_state=DATASET_CONFIG["random_state"]
    )

    # Standardize features (fit on train, transform both)
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    print(f" Data split: {len(X_train)} train, {len(X_test)} test samples")
    print(f" Features standardized (mean≈0, std≈1)")

    # Verify standardization
    print(f" Train features - Mean: {X_train_scaled.mean(axis=0)}")
    print(f" Train features - Std:  {X_train_scaled.std(axis=0)}")

    return X_train_scaled, X_test_scaled, y_train, y_test, scaler

def save_standardized_data(X_train: np.ndarray, X_test: np.ndarray,
                          y_train: np.ndarray, y_test: np.ndarray,
                          X_full: np.ndarray, y_full: np.ndarray,
                          feature_names: list, scaler: StandardScaler):
    """Save all data with high precision and metadata."""

    # Create DataFrames with proper column names
    train_df = pd.DataFrame(X_train, columns=feature_names)
    train_df[DATASET_CONFIG["target_name"]] = y_train

    test_df = pd.DataFrame(X_test, columns=feature_names)
    test_df[DATASET_CONFIG["target_name"]] = y_test

    full_df = pd.DataFrame(X_full, columns=feature_names)
    full_df[DATASET_CONFIG["target_name"]] = y_full

    # Save to CSV files with high precision
    train_path = "../data/california_train_standardized.csv"
    test_path = "../data/california_test_standardized.csv"
    full_path = "../data/california_full_standardized.csv"

    train_df.to_csv(train_path, index=False, float_format='%.8f')
    test_df.to_csv(test_path, index=False, float_format='%.8f')
    full_df.to_csv(full_path, index=False, float_format='%.8f')

    print(f" Saved training data: {train_path} ({len(train_df)} samples)")
    print(f" Saved test data: {test_path} ({len(test_df)} samples)")
    print(f" Saved full data: {full_path} ({len(full_df)} samples)")

    # Save preprocessing metadata
    metadata = {
        "dataset_config": DATASET_CONFIG,
        "feature_names": feature_names,
        "scaler_mean": scaler.mean_.tolist(),
        "scaler_scale": scaler.scale_.tolist(),
        "train_samples": len(train_df),
        "test_samples": len(test_df),
        "total_samples": len(full_df)
    }

    metadata_path = "../data/preprocessing_metadata.json"
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)

    print(f" Saved preprocessing metadata: {metadata_path}")

    return train_path, test_path, full_path, metadata_path

def compute_file_checksum(filepath: str) -> str:
    """Compute SHA-256 checksum of a file."""
    sha256_hash = hashlib.sha256()
    with open(filepath, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            sha256_hash.update(chunk)
    return sha256_hash.hexdigest()

def create_checksums(file_paths: list) -> Dict[str, str]:
    """Create checksums for all data files."""
    print("\n Computing SHA-256 checksums for data integrity...")

    checksums = {}
    for filepath in file_paths:
        filename = os.path.basename(filepath)
        checksum = compute_file_checksum(filepath)
        checksums[filename] = checksum
        print(f" {filename}: {checksum[:16]}...")

    # Save checksums
    checksum_path = "../data/data_checksums.json"
    with open(checksum_path, 'w') as f:
        json.dump(checksums, f, indent=2)

    print(f" Saved checksums: {checksum_path}")
    return checksums

def verify_data_consistency(train_path: str, test_path: str, full_path: str):
    """Verify the created data meets consistency requirements."""
    print("\n Verifying data consistency...")

    # Load all datasets
    train_df = pd.read_csv(train_path)
    test_df = pd.read_csv(test_path)
    full_df = pd.read_csv(full_path)

    # Extract features and targets
    feature_cols = [col for col in train_df.columns if col != DATASET_CONFIG["target_name"]]

    X_train = train_df[feature_cols].values
    y_train = train_df[DATASET_CONFIG["target_name"]].values
    X_test = test_df[feature_cols].values
    y_test = test_df[DATASET_CONFIG["target_name"]].values
    X_full = full_df[feature_cols].values
    y_full = full_df[DATASET_CONFIG["target_name"]].values

    # Verify shapes
    assert X_train.shape[1] == DATASET_CONFIG["features"], f"Train features: expected {DATASET_CONFIG['features']}, got {X_train.shape[1]}"
    assert X_test.shape[1] == DATASET_CONFIG["features"], f"Test features: expected {DATASET_CONFIG['features']}, got {X_test.shape[1]}"
    assert X_full.shape[1] == DATASET_CONFIG["features"], f"Full features: expected {DATASET_CONFIG['features']}, got {X_full.shape[1]}"

    # Verify sample counts
    expected_train = int(DATASET_CONFIG["subset_size"] * (1 - DATASET_CONFIG["test_size"]))
    expected_test = DATASET_CONFIG["subset_size"] - expected_train

    assert len(X_train) == expected_train, f"Train samples: expected {expected_train}, got {len(X_train)}"
    assert len(X_test) == expected_test, f"Test samples: expected {expected_test}, got {len(X_test)}"
    assert len(X_full) == DATASET_CONFIG["subset_size"], f"Full samples: expected {DATASET_CONFIG['subset_size']}, got {len(X_full)}"

    # Verify standardization (training data should have mean≈0, std≈1)
    train_means = X_train.mean(axis=0)
    train_stds = X_train.std(axis=0)

    assert np.allclose(train_means, 0, atol=1e-8), f"Train means not zero: {train_means}"
    assert np.allclose(train_stds, 1, atol=1e-8), f"Train stds not one: {train_stds}"

    print(" Shape verification passed")
    print(" Sample count verification passed")
    print(" Standardization verification passed")
    print(" All data consistency checks passed!")

def print_dataset_summary(train_path: str, test_path: str, full_path: str, metadata_path: str):
    """Print comprehensive dataset summary."""
    print("\n" + "="*80)
    print("STANDARDIZED CALIFORNIA HOUSING DATASET SUMMARY")
    print("="*80)

    # Load metadata
    with open(metadata_path, 'r') as f:
        metadata = json.load(f)

    config = metadata["dataset_config"]

    print(f" Dataset Configuration:")
    print(f"   Total samples: {config['subset_size']}")
    print(f"   Train/test split: {100*(1-config['test_size']):.0f}%/{100*config['test_size']:.0f}%")
    print(f"   Random seed: {config['random_state']}")
    print(f"   Features: {config['features']}")
    print(f"   Stratification bins: {config['stratify_bins']}")

    print(f"\n📁 Generated Files:")
    print(f"   Training data: {os.path.basename(train_path)} ({metadata['train_samples']} samples)")
    print(f"   Test data: {os.path.basename(test_path)} ({metadata['test_samples']} samples)")
    print(f"   Full data: {os.path.basename(full_path)} ({metadata['total_samples']} samples)")
    print(f"   Metadata: {os.path.basename(metadata_path)}")
    print(f"   Checksums: data_checksums.json")

    print(f"\n Preprocessing Applied:")
    print(f"   StandardScaler fitted on training data")
    print(f"   Features standardized: mean≈0, std≈1")
    print(f"   High precision CSV format (8 decimal places)")

    print(f"\n Shape Safety Constraints:")
    print(f"   Input shape:  [batch_size, {config['features']}] - exactly {config['features']} features required")
    print(f"   Weight shape: [{config['features']}, 1] - matches input feature count")
    print(f"   Output shape: [batch_size, 1] - single continuous prediction")

    print(f"\n Ready for consistent linear regression experiments!")

def main():
    parser = argparse.ArgumentParser(description="Create standardized California Housing subset")
    parser.add_argument("--subset-size", type=int, default=1000,
                       help="Size of the subset to create (default: 1000)")
    parser.add_argument("--test-size", type=float, default=0.3,
                       help="Proportion for test set (default: 0.3)")
    parser.add_argument("--random-seed", type=int, default=42,
                       help="Random seed for reproducibility (default: 42)")
    args = parser.parse_args()

    # Update configuration with command line arguments
    DATASET_CONFIG["subset_size"] = args.subset_size
    DATASET_CONFIG["test_size"] = args.test_size
    DATASET_CONFIG["random_state"] = args.random_seed

    print(" Creating Standardized California Housing Dataset")
    print("=" * 60)
    print(f"Configuration: {DATASET_CONFIG}")
    print()

    # Create data directory
    create_data_directory()

    # Load full dataset
    X_full, y_full, feature_names = load_full_california_housing()

    # Create stratified subset
    X_subset, y_subset = create_stratified_subset(
        X_full, y_full, DATASET_CONFIG["subset_size"], DATASET_CONFIG["random_state"]
    )

    # Split and standardize
    X_train, X_test, y_train, y_test, scaler = split_and_standardize_data(X_subset, y_subset)

    # Prepare full standardized subset
    X_subset_scaled = scaler.fit_transform(X_subset)

    # Save all data
    train_path, test_path, full_path, metadata_path = save_standardized_data(
        X_train, X_test, y_train, y_test, X_subset_scaled, y_subset, feature_names, scaler
    )

    # Create checksums
    checksums = create_checksums([train_path, test_path, full_path, metadata_path])

    # Verify consistency
    verify_data_consistency(train_path, test_path, full_path)

    # Print summary
    print_dataset_summary(train_path, test_path, full_path, metadata_path)

    print(f"\n Standardized dataset creation completed successfully!")
    return 0

if __name__ == "__main__":
    exit(main())