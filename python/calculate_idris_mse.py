#!/usr/bin/env python3
"""
Calculate Test MSE for Idris and Spidr models using their prediction outputs
"""

import numpy as np
import pandas as pd
import json
import os

def load_test_data():
    """Load the standardized test data"""
    try:
        test_df = pd.read_csv('../data/california_test_standardized.csv')
        return test_df['target'].values
    except FileNotFoundError:
        print("Error: Test data file not found")
        return None

def load_predictions(filename):
    """Load predictions from a text file"""
    try:
        predictions = np.loadtxt(f'../results/{filename}')
        return predictions
    except FileNotFoundError:
        print(f"Error: {filename} not found")
        return None
    except Exception as e:
        print(f"Error loading {filename}: {e}")
        return None

def calculate_metrics(y_true, y_pred, model_name):
    """Calculate MSE and R² metrics"""
    if y_true is None or y_pred is None:
        return None, None
    
    # Ensure same length
    min_len = min(len(y_true), len(y_pred))
    y_true = y_true[:min_len]
    y_pred = y_pred[:min_len]
    
    # Calculate MSE
    mse = np.mean((y_true - y_pred) ** 2)
    
    # Calculate R²
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    r2 = 1 - (ss_res / ss_tot)
    
    print(f"\n{model_name} Results:")
    print(f"  Samples used: {min_len}")
    print(f"  Test MSE: {mse:.6f}")
    print(f"  Test R²: {r2:.6f}")
    print(f"  Prediction range: [{np.min(y_pred):.3f}, {np.max(y_pred):.3f}]")
    print(f"  True target range: [{np.min(y_true):.3f}, {np.max(y_true):.3f}]")
    
    return mse, r2

def main():
    print("Calculating Test MSE for Idris2 Models")
    print("=" * 50)
    
    # Load test targets
    y_test = load_test_data()
    if y_test is None:
        return
    
    print(f"Test data loaded: {len(y_test)} samples")
    print(f"Target range: [{np.min(y_test):.3f}, {np.max(y_test):.3f}]")
    
    # Load and evaluate Idris predictions
    idris_pred = load_predictions('idris_linear_predictions.txt')
    idris_mse, idris_r2 = calculate_metrics(y_test, idris_pred, "Idris")
    
    # Load and evaluate Spidr predictions
    spidr_pred = load_predictions('spidr_linear_predictions.txt')
    spidr_mse, spidr_r2 = calculate_metrics(y_test, spidr_pred, "Spidr")
    
    # Summary table
    print("\n" + "=" * 50)
    print("SUMMARY TABLE")
    print("=" * 50)
    print(f"{'Model':<10} {'Test MSE':<12} {'Test R²':<10} {'Status'}")
    print("-" * 50)
    
    if idris_mse is not None:
        print(f"{'Idris':<10} {idris_mse:<12.6f} {idris_r2:<10.6f} {'✅'}")
    else:
        print(f"{'Idris':<10} {'N/A':<12} {'N/A':<10} {'❌'}")
    
    if spidr_mse is not None:
        print(f"{'Spidr':<10} {spidr_mse:<12.6f} {spidr_r2:<10.6f} {'✅'}")
    else:
        print(f"{'Spidr':<10} {'N/A':<12} {'N/A':<10} {'❌'}")
    
    # Check for potential issues
    print("\n" + "=" * 50)
    print("DIAGNOSTIC INFORMATION")
    print("=" * 50)
    
    if spidr_pred is not None:
        unique_spidr = len(np.unique(spidr_pred))
        print(f"Spidr unique predictions: {unique_spidr}")
        if unique_spidr == 1:
            print("⚠️  WARNING: Spidr is predicting the same value for all samples")
            print("   This suggests the model may not have trained properly")
    
    if idris_pred is not None:
        unique_idris = len(np.unique(idris_pred))
        print(f"Idris unique predictions: {unique_idris}")
        if unique_idris == len(idris_pred):
            print("✅ Idris predictions show good variance")
        elif unique_idris == 1:
            print("⚠️  WARNING: Idris is predicting the same value for all samples")

if __name__ == "__main__":
    main()
