#!/usr/bin/env python3
"""
Standalone data consistency verification script.
Verifies checksums, data integrity, and consistency across all implementations.

Note: this is a simple wrapper around the main verification logic
"""

import sys
import os
from unified_data_loader import verify_data_setup, UnifiedDataLoader
import argparse
import warnings

# suppress warnings for cleaner verification output
warnings.filterwarnings("ignore")

def main():
    parser = argparse.ArgumentParser(description="Verify data consistency for linear regression experiments")
    parser.add_argument("--data-dir", default="../data", help="Data directory path")
    parser.add_argument("--quiet", action="store_true", help="Suppress verbose output")
    args = parser.parse_args()

    print(" DATA CONSISTENCY VERIFICATION")
    print("=" * 50)

    verbose = not args.quiet

    # Run comprehensive verification
    success = verify_data_setup(verbose=verbose)

    if success:
        print("\n ALL DATA CONSISTENCY CHECKS PASSED!")
        print(" Ready for fair linear regression comparison experiments")
        return 0
    else:
        print("\n DATA CONSISTENCY VERIFICATION FAILED!")
        print(" Please regenerate standardized dataset before running experiments")
        return 1

if __name__ == "__main__":
    exit(main())