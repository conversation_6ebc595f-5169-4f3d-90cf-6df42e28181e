#!/usr/bin/env python3
"""
Comprehensive test suite for data consistency validation.
Tests data integrity, consistency, and cross-implementation compatibility.

TODO: these tests are getting pretty extensive, maybe group them better
"""

import unittest
import numpy as np
import pandas as pd
import os
import json
import hashlib
from unified_data_loader import UnifiedDataLoader, load_standardized_data, verify_data_setup
from typing import Tuple
import warnings

# suppress test warnings for cleaner output
warnings.filterwarnings("ignore")

class TestDataConsistency(unittest.TestCase):
    """Test suite for data consistency validation."""

    def setUp(self):
        """Set up test fixtures."""
        self.data_dir = "../data"
        self.loader = UnifiedDataLoader(self.data_dir)

        # Expected configuration
        self.expected_config = {
            "subset_size": 1429,
            "test_size": 0.3,
            "random_state": 123,
            "features": 8,
            "target_name": "target"
        }

    def test_required_files_exist(self):
        """Test that all required data files exist."""
        required_files = [
            "california_train_standardized.csv",
            "california_test_standardized.csv",
            "california_full_standardized.csv",
            "preprocessing_metadata.json",
            "data_checksums.json"
        ]

        for filename in required_files:
            filepath = os.path.join(self.data_dir, filename)
            self.assertTrue(os.path.exists(filepath), f"Required file missing: {filename}")

    def test_checksum_verification(self):
        """Test that all data files pass checksum verification."""
        self.assertTrue(self.loader.verify_data_integrity(verbose=False))

    def test_metadata_structure(self):
        """Test that metadata has correct structure and values."""
        metadata = self.loader._load_metadata()

        # Check required keys
        required_keys = ["dataset_config", "feature_names", "scaler_mean", "scaler_scale",
                        "train_samples", "test_samples", "total_samples"]
        for key in required_keys:
            self.assertIn(key, metadata, f"Missing metadata key: {key}")

        # Check dataset configuration
        config = metadata["dataset_config"]
        for key, expected_value in self.expected_config.items():
            self.assertEqual(config[key], expected_value, f"Config mismatch for {key}")

        # Check feature names
        feature_names = metadata["feature_names"]
        expected_features = ["MedInc", "HouseAge", "AveRooms", "AveBedrms",
                           "Population", "AveOccup", "Latitude", "Longitude"]
        self.assertEqual(feature_names, expected_features)

        # Check sample counts
        self.assertEqual(metadata["train_samples"], 1000)
        self.assertEqual(metadata["test_samples"], 429)
        self.assertEqual(metadata["total_samples"], 1429)

    def test_data_shapes(self):
        """Test that loaded data has correct shapes."""
        X_train, X_test, y_train, y_test = self.loader.load_train_test_data(verbose=False)

        # Check training data shapes
        self.assertEqual(X_train.shape, (1000, 8), "Training features shape incorrect")
        self.assertEqual(y_train.shape, (1000,), "Training targets shape incorrect")

        # Check test data shapes
        self.assertEqual(X_test.shape, (429, 8), "Test features shape incorrect")
        self.assertEqual(y_test.shape, (429,), "Test targets shape incorrect")

    def test_standardization_properties(self):
        """Test that training data is properly standardized."""
        X_train, X_test, y_train, y_test = self.loader.load_train_test_data(verbose=False)

        # Training data should have mean ≈ 0 and std ≈ 1
        train_means = X_train.mean(axis=0)
        train_stds = X_train.std(axis=0)

        np.testing.assert_allclose(train_means, 0, atol=1e-8,
                                 err_msg="Training data means not zero")
        np.testing.assert_allclose(train_stds, 1, atol=1e-8,
                                 err_msg="Training data stds not one")

    def test_data_consistency_across_files(self):
        """Test consistency between train/test split and full dataset."""
        # Load train/test data
        X_train, X_test, y_train, y_test = self.loader.load_train_test_data(verbose=False)

        # Load full data
        X_full, y_full = self.loader.load_full_data(verbose=False)

        # Check that full dataset size matches train + test
        self.assertEqual(len(X_full), len(X_train) + len(X_test))
        self.assertEqual(len(y_full), len(y_train) + len(y_test))

        # Check feature consistency
        self.assertEqual(X_full.shape[1], X_train.shape[1])
        self.assertEqual(X_full.shape[1], X_test.shape[1])

    def test_no_missing_values(self):
        """Test that there are no missing values in the data."""
        X_train, X_test, y_train, y_test = self.loader.load_train_test_data(verbose=False)

        # Check for NaN values
        self.assertFalse(np.isnan(X_train).any(), "Training features contain NaN")
        self.assertFalse(np.isnan(X_test).any(), "Test features contain NaN")
        self.assertFalse(np.isnan(y_train).any(), "Training targets contain NaN")
        self.assertFalse(np.isnan(y_test).any(), "Test targets contain NaN")

        # Check for infinite values
        self.assertFalse(np.isinf(X_train).any(), "Training features contain inf")
        self.assertFalse(np.isinf(X_test).any(), "Test features contain inf")
        self.assertFalse(np.isinf(y_train).any(), "Training targets contain inf")
        self.assertFalse(np.isinf(y_test).any(), "Test targets contain inf")

    def test_target_value_ranges(self):
        """Test that target values are in reasonable ranges."""
        X_train, X_test, y_train, y_test = self.loader.load_train_test_data(verbose=False)

        # California Housing prices should be positive
        self.assertTrue((y_train > 0).all(), "Training targets contain non-positive values")
        self.assertTrue((y_test > 0).all(), "Test targets contain non-positive values")

        # Reasonable price range (in hundreds of thousands)
        self.assertTrue((y_train < 10).all(), "Training targets unreasonably high")
        self.assertTrue((y_test < 10).all(), "Test targets unreasonably high")

    def test_feature_value_ranges(self):
        """Test that standardized features are in reasonable ranges."""
        X_train, X_test, y_train, y_test = self.loader.load_train_test_data(verbose=False)

        # Standardized features should mostly be within [-3, 3] (3 standard deviations)
        # Allow some outliers but not too many
        outlier_threshold = 0.05  # 5% outliers allowed

        for i in range(X_train.shape[1]):
            train_outliers = np.abs(X_train[:, i]) > 3
            test_outliers = np.abs(X_test[:, i]) > 3

            train_outlier_rate = train_outliers.mean()
            test_outlier_rate = test_outliers.mean()

            self.assertLess(train_outlier_rate, outlier_threshold,
                          f"Too many outliers in training feature {i}")
            self.assertLess(test_outlier_rate, outlier_threshold,
                          f"Too many outliers in test feature {i}")

    def test_reproducibility(self):
        """Test that data loading is reproducible."""
        # Load data twice
        X_train1, X_test1, y_train1, y_test1 = self.loader.load_train_test_data(verbose=False)
        X_train2, X_test2, y_train2, y_test2 = self.loader.load_train_test_data(verbose=False)

        # Should be identical
        np.testing.assert_array_equal(X_train1, X_train2, "Training features not reproducible")
        np.testing.assert_array_equal(X_test1, X_test2, "Test features not reproducible")
        np.testing.assert_array_equal(y_train1, y_train2, "Training targets not reproducible")
        np.testing.assert_array_equal(y_test1, y_test2, "Test targets not reproducible")

class TestCrossImplementationCompatibility(unittest.TestCase):
    """Test compatibility across different implementations."""

    def setUp(self):
        """Set up test fixtures."""
        self.loader = UnifiedDataLoader("../data")

    def test_unified_data_loader_function(self):
        """Test the convenience function works correctly."""
        X_train1, X_test1, y_train1, y_test1 = load_standardized_data(verbose=False)
        X_train2, X_test2, y_train2, y_test2 = self.loader.load_train_test_data(verbose=False)

        # Should be identical
        np.testing.assert_array_equal(X_train1, X_train2)
        np.testing.assert_array_equal(X_test1, X_test2)
        np.testing.assert_array_equal(y_train1, y_train2)
        np.testing.assert_array_equal(y_test1, y_test2)

    def test_verify_data_setup_function(self):
        """Test the data setup verification function."""
        self.assertTrue(verify_data_setup(verbose=False))

    def test_dataset_info_consistency(self):
        """Test that dataset info is consistent."""
        X_train, X_test, y_train, y_test = self.loader.load_train_test_data(verbose=False)
        dataset_info = self.loader.get_dataset_info()

        self.assertEqual(dataset_info.train_samples, len(X_train))
        self.assertEqual(dataset_info.test_samples, len(X_test))
        self.assertEqual(dataset_info.features, X_train.shape[1])
        self.assertEqual(dataset_info.train_shape, X_train.shape)
        self.assertEqual(dataset_info.test_shape, X_test.shape)
        self.assertTrue(dataset_info.preprocessing_applied)
        self.assertTrue(dataset_info.checksums_verified)

    def test_preprocessing_info_consistency(self):
        """Test that preprocessing info is consistent."""
        preprocessing_info = self.loader.get_preprocessing_info()

        required_keys = ["scaler_mean", "scaler_scale", "feature_names", "dataset_config"]
        for key in required_keys:
            self.assertIn(key, preprocessing_info)

        # Check that scaler parameters have correct length
        self.assertEqual(len(preprocessing_info["scaler_mean"]), 8)
        self.assertEqual(len(preprocessing_info["scaler_scale"]), 8)
        self.assertEqual(len(preprocessing_info["feature_names"]), 8)

class TestDataIntegrity(unittest.TestCase):
    """Test data integrity and security."""

    def setUp(self):
        """Set up test fixtures."""
        self.data_dir = "../data"
        self.loader = UnifiedDataLoader(self.data_dir)

    def test_checksum_file_integrity(self):
        """Test that checksum file itself is valid."""
        checksums_path = os.path.join(self.data_dir, "data_checksums.json")
        self.assertTrue(os.path.exists(checksums_path))

        with open(checksums_path, 'r') as f:
            checksums = json.load(f)

        # Should have checksums for all required files
        required_files = [
            "california_train_standardized.csv",
            "california_test_standardized.csv",
            "california_full_standardized.csv",
            "preprocessing_metadata.json"
        ]

        for filename in required_files:
            self.assertIn(filename, checksums)
            # Checksums should be 64-character hex strings (SHA-256)
            self.assertEqual(len(checksums[filename]), 64)
            self.assertTrue(all(c in '0123456789abcdef' for c in checksums[filename]))

    def test_file_modification_detection(self):
        """Test that file modifications would be detected."""
        # This test verifies the checksum mechanism works
        # We'll compute checksums manually and compare

        checksums = self.loader._load_checksums()

        for filename, expected_checksum in checksums.items():
            if filename.endswith('.csv') or filename.endswith('.json'):
                filepath = os.path.join(self.data_dir, filename)
                actual_checksum = self.loader._compute_file_checksum(filepath)
                self.assertEqual(actual_checksum, expected_checksum,
                               f"Checksum mismatch for {filename}")

    def test_csv_format_consistency(self):
        """Test that CSV files have consistent format."""
        # Load and check CSV structure
        train_df = pd.read_csv(os.path.join(self.data_dir, "california_train_standardized.csv"))
        test_df = pd.read_csv(os.path.join(self.data_dir, "california_test_standardized.csv"))
        full_df = pd.read_csv(os.path.join(self.data_dir, "california_full_standardized.csv"))

        # Check column consistency
        self.assertEqual(list(train_df.columns), list(test_df.columns))
        self.assertEqual(list(train_df.columns), list(full_df.columns))

        # Check expected columns
        expected_columns = ["MedInc", "HouseAge", "AveRooms", "AveBedrms",
                          "Population", "AveOccup", "Latitude", "Longitude", "target"]
        self.assertEqual(list(train_df.columns), expected_columns)

        # Check data types (should all be numeric)
        for df in [train_df, test_df, full_df]:
            for col in df.columns:
                self.assertTrue(pd.api.types.is_numeric_dtype(df[col]),
                              f"Column {col} is not numeric")

def run_all_tests():
    """Run all data consistency tests."""
    print(" Running Data Consistency Test Suite")
    print("=" * 60)

    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add test classes
    suite.addTests(loader.loadTestsFromTestCase(TestDataConsistency))
    suite.addTests(loader.loadTestsFromTestCase(TestCrossImplementationCompatibility))
    suite.addTests(loader.loadTestsFromTestCase(TestDataIntegrity))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")

    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"  {test}: {traceback}")

    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"  {test}: {traceback}")

    success = len(result.failures) == 0 and len(result.errors) == 0

    if success:
        print("\n ALL TESTS PASSED!")
        print(" Data consistency verified across all implementations")
    else:
        print("\n SOME TESTS FAILED!")
        print(" Data consistency issues detected")

    return success

if __name__ == "__main__":
    import sys
    success = run_all_tests()
    sys.exit(0 if success else 1)