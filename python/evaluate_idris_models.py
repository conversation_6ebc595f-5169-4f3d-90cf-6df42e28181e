#!/usr/bin/env python3
"""
Evaluate <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> models on full test set using their trained weights
"""

import numpy as np
import pandas as pd
import os

def load_test_data():
    """Load the standardized test data"""
    try:
        test_df = pd.read_csv('../data/california_test_standardized.csv')
        X_test = test_df.iloc[:, :-1].values  # All columns except last (target)
        y_test = test_df['target'].values     # Last column (target)
        return X_test, y_test
    except FileNotFoundError:
        print("Error: Test data file not found")
        return None, None

def load_model_weights(model_name):
    """Load weights and bias for a model"""
    try:
        weights_file = f'../results/{model_name}_linear_weights.txt'
        bias_file = f'../results/{model_name}_linear_bias.txt'
        
        weights = np.loadtxt(weights_file)
        with open(bias_file, 'r') as f:
            bias = float(f.read().strip())
        
        # Ensure weights are in column vector format
        if weights.ndim == 1:
            weights = weights.reshape(-1, 1)
        
        return weights, bias
    except Exception as e:
        print(f"Error loading {model_name} weights: {e}")
        return None, None

def predict_linear(X, weights, bias):
    """Make predictions using linear model"""
    return X @ weights + bias

def calculate_metrics(y_true, y_pred, model_name):
    """Calculate MSE and R² metrics"""
    # Flatten predictions if needed
    if y_pred.ndim > 1:
        y_pred = y_pred.flatten()
    
    # Calculate MSE
    mse = np.mean((y_true - y_pred) ** 2)
    
    # Calculate R²
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    r2 = 1 - (ss_res / ss_tot)
    
    print(f"\n{model_name} Results:")
    print(f"  Test MSE: {mse:.6f}")
    print(f"  Test R²: {r2:.6f}")
    print(f"  Prediction range: [{np.min(y_pred):.3f}, {np.max(y_pred):.3f}]")
    print(f"  Prediction std: {np.std(y_pred):.3f}")
    
    return mse, r2

def main():
    print("Evaluating Idris2 Models on Full Test Set")
    print("=" * 50)
    
    # Load test data
    X_test, y_test = load_test_data()
    if X_test is None:
        return
    
    print(f"Test data loaded: {X_test.shape[0]} samples, {X_test.shape[1]} features")
    print(f"Target range: [{np.min(y_test):.3f}, {np.max(y_test):.3f}]")
    
    results = {}
    
    # Evaluate Idris model
    print("\n" + "-" * 30)
    print("IDRIS MODEL EVALUATION")
    print("-" * 30)
    
    idris_weights, idris_bias = load_model_weights('idris')
    if idris_weights is not None:
        print(f"Idris weights shape: {idris_weights.shape}")
        print(f"Idris bias: {idris_bias:.6f}")
        
        idris_pred = predict_linear(X_test, idris_weights, idris_bias)
        idris_mse, idris_r2 = calculate_metrics(y_test, idris_pred, "Idris")
        results['Idris'] = {'mse': idris_mse, 'r2': idris_r2}
    else:
        print("❌ Could not load Idris model weights")
        results['Idris'] = {'mse': None, 'r2': None}
    
    # Evaluate Spidr model
    print("\n" + "-" * 30)
    print("SPIDR MODEL EVALUATION")
    print("-" * 30)
    
    spidr_weights, spidr_bias = load_model_weights('spidr')
    if spidr_weights is not None:
        print(f"Spidr weights shape: {spidr_weights.shape}")
        print(f"Spidr bias: {spidr_bias:.6f}")
        
        spidr_pred = predict_linear(X_test, spidr_weights, spidr_bias)
        spidr_mse, spidr_r2 = calculate_metrics(y_test, spidr_pred, "Spidr")
        results['Spidr'] = {'mse': spidr_mse, 'r2': spidr_r2}
    else:
        print("❌ Could not load Spidr model weights")
        results['Spidr'] = {'mse': None, 'r2': None}
    
    # Summary table
    print("\n" + "=" * 60)
    print("FINAL RESULTS - FULL TEST SET EVALUATION")
    print("=" * 60)
    print(f"{'Model':<10} {'Test MSE':<12} {'Test R²':<10} {'Status'}")
    print("-" * 60)
    
    for model, metrics in results.items():
        if metrics['mse'] is not None:
            print(f"{model:<10} {metrics['mse']:<12.6f} {metrics['r2']:<10.6f} {'✅'}")
        else:
            print(f"{model:<10} {'N/A':<12} {'N/A':<10} {'❌'}")
    
    # Compare with your table values
    print("\n" + "=" * 60)
    print("COMPARISON WITH PROVIDED TABLE")
    print("=" * 60)
    print("Your table values:")
    print("  Idris: MSE=0.597649, R²=0.5899")
    print("  Spidr: MSE=0.597918, R²=0.5897")
    print("\nCalculated values:")
    if results['Idris']['mse'] is not None:
        print(f"  Idris: MSE={results['Idris']['mse']:.6f}, R²={results['Idris']['r2']:.6f}")
    if results['Spidr']['mse'] is not None:
        print(f"  Spidr: MSE={results['Spidr']['mse']:.6f}, R²={results['Spidr']['r2']:.6f}")

if __name__ == "__main__":
    main()
