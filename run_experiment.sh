#!/bin/bash

# Linear Regression with Shape Safety Experiment Automation Script
# Demonstrates compile-time matrix dimension verification vs runtime checking
# TODO: this script is getting pretty long, maybe split into modules later

set -e  # Exit on any error

# Color codes for output (probably overkill but looks nice)
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration (hardcoded for now)
LEARNING_RATE=0.01
MAX_ITERATIONS=1000
REGULARIZATION=0.001
TEST_SIZE=0.3
RANDOM_SEED=123  # the answer to everything

print_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${PURPLE} Linear Regression with Shape Safety${NC}"
    echo -e "${PURPLE}========================================${NC}"
    echo ""
}

print_step() {
    echo -e "${CYAN} $1${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN} $1${NC}"
    echo ""
}

print_warning() {
    echo -e "${YELLOW}  $1${NC}"
    echo ""
}

print_error() {
    echo -e "${RED} $1${NC}"
    echo ""
}

check_dependencies() {
    print_step "Checking dependencies..."

    # Check Python dependencies (basic stuff)
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 is required but not installed"
        exit 1
    fi

    # Check required Python packages (this list keeps growing...)
    python3 -c "import numpy, pandas, torch, sklearn, matplotlib, seaborn" 2>/dev/null || {
        print_error "Required Python packages missing. Install with:"
        echo "pip install numpy pandas torch scikit-learn matplotlib seaborn"
        exit 1
    }

    # Check Idris2 and pack (the tricky part - installation can be finicky)
    if ! command -v pack &> /dev/null; then
        print_error "pack (Idris2 package manager) is required but not installed"
        echo "Install from: https://github.com/stefan-hoeck/idris2-pack"
        exit 1
    fi

    if ! command -v idris2 &> /dev/null; then
        print_error "Idris2 is required but not installed"
        echo "Install with: pack switch latest"
        exit 1
    fi
    
    print_success "All dependencies available"
}

setup_directories() {
    print_step "Setting up project directories..."
    
    mkdir -p data
    mkdir -p results
    mkdir -p src
    mkdir -p python
    
    print_success "Directory structure created"
}

process_dataset() {
    print_step "Processing standardized California Housing dataset..."

    cd python

    # Check if standardized data exists
    if [ ! -f "../data/california_train_standardized.csv" ] || [ ! -f "../data/california_test_standardized.csv" ]; then
        echo " Creating standardized California Housing dataset..."
        python3 create_standardized_dataset.py --subset-size 1429 --test-size ${TEST_SIZE} --random-seed ${RANDOM_SEED}  # 1429 @ 70% -> 1000 train
        if [ $? -eq 0 ]; then
            print_success "Standardized dataset created"
        else
            print_error "Dataset creation failed"
            exit 1
        fi
    else
        print_warning "Standardized dataset already exists"
    fi

    # Verify data consistency
    echo " Verifying data consistency and integrity..."
    python3 verify_data_consistency.py
    if [ $? -eq 0 ]; then
        print_success "Data consistency verified"
    else
        print_error "Data consistency verification failed"
        exit 1
    fi

    # Run comprehensive tests
    echo " Running data validation tests..."
    python3 test_data_consistency.py
    if [ $? -eq 0 ]; then
        print_success "All data validation tests passed"
    else
        print_error "Data validation tests failed"
        exit 1
    fi

    cd ..
}

install_spidr() {
    print_step "Installing Spidr dependencies..."
    
    # Check if pack is available
    if ! command -v pack &> /dev/null; then
        print_warning "Pack package manager not available - skipping Spidr installation"
        print_warning "Spidr experiment will use mock implementation"
        return 0
    fi

    # Check if spidr is already installed
    if pack query spidr &> /dev/null; then
        print_warning "Spidr already installed"
    else
        print_step "Installing PJRT plugin (CPU)..."
        pack install pjrt-plugin-xla-cpu || {
            print_warning "Failed to install PJRT plugin - continuing anyway"
        }

        print_step "Installing Spidr..."
        pack install spidr || {
            print_warning "Failed to install Spidr - using mock implementation"
            return 0
        }

        print_success "Spidr installation completed"
    fi
}

build_idris_project() {
    print_step "Building Idris linear regression implementation..."

    # Build the main project (fingers crossed it compiles)
    idris2 --build linear-regression.ipkg || {
        print_error "Idris build failed"
        echo "Check your .ipkg file and Idris source code for errors"
        exit 1
    }

    # Build the Spidr project (mock implementation - might not work without real Spidr)
    idris2 --build linear-regression-spidr.ipkg || {
        print_warning "Spidr build failed - continuing with main implementation only"
    }

    print_success "Idris project built successfully"
}

run_idris_experiment() {
    print_step "Running Idris shape-safe linear regression..."
    
    echo " Shape-safe linear regression with compile-time guarantees:"
    echo "   - Matrix shape verification: [m, 8] @ [8, 1] → [m, 1]"
    echo "   - Feature count constraint: exactly 8 features required"
    echo "   - Learning rate: ${LEARNING_RATE}"
    echo "   - Max iterations: ${MAX_ITERATIONS}"
    echo "   - Regularization: ${REGULARIZATION}"
    echo ""
    
    # Run the Idris implementation with shell-level timing and memory (macOS /usr/bin/time -l)
    start_ts=$(date +%s)
    /usr/bin/time -l ./build/exec/linear-regression 2> results/idris_time_mem.txt || {
        print_error "Idris execution failed"
        echo "Check the runtime output for errors"
        exit 1
    }
    end_ts=$(date +%s)
    echo $((end_ts - start_ts)) > results/idris_time_seconds.txt
    
    if [ -f "results/idris_linear_weights.txt" ] && [ -f "results/idris_linear_bias.txt" ]; then
        print_success "Idris experiment completed"
        
        # Show shape verification results
        echo -e "${BLUE} Shape Safety Verification Results:${NC}"
        python3 -c "
import numpy as np
try:
    weights = np.loadtxt('results/idris_linear_weights.txt')
    with open('results/idris_linear_bias.txt', 'r') as f:
        bias = float(f.read().strip())
    
    print(f'    Weights shape: {weights.shape} (expecting 8 features)')
    print(f'    Bias value: {bias:.6f}')
    print(f'    All matrix operations shape-verified at compile time')
    print(f'    No runtime shape errors possible!')
except Exception as e:
    print(f'    Error reading results: {e}')
"
    else
        print_error "Idris experiment did not produce expected output"
        exit 1
    fi
}

run_spidr_experiment() {
    print_step "Running Spidr linear regression experiment..."

    echo " Spidr implementation with automatic differentiation:"
    echo "   - Compile-time shape safety"
    echo "   - Automatic gradient computation"
    echo "   - Type-safe tensor operations"
    echo "   - Zero runtime shape errors"
    echo ""

    # Build Spidr implementation
    idris2 --build linear-regression-spidr.ipkg || {
        print_error "Spidr build failed"
        exit 1
    }

    # Run Spidr implementation with shell-level timing and memory
    start_ts=$(date +%s)
    /usr/bin/time -l ./build/exec/linear-regression-spidr 2> results/spidr_time_mem.txt || {
        print_error "Spidr experiment failed"
        exit 1
    }
    end_ts=$(date +%s)
    echo $((end_ts - start_ts)) > results/spidr_time_seconds.txt

    if [ -f "results/spidr_linear_weights.txt" ] && [ -f "results/spidr_linear_bias.txt" ]; then
        print_success "Spidr experiment completed"

        # Show Spidr results
        echo -e "${BLUE} Spidr Auto-Diff Results:${NC}"
        python3 -c "
import numpy as np
try:
    weights = np.loadtxt('results/spidr_linear_weights.txt')
    with open('results/spidr_linear_bias.txt', 'r') as f:
        bias = float(f.read().strip())

    print(f'    Weights shape: {weights.shape} (expecting 8 features)')
    print(f'    Bias value: {bias:.6f}')
    print(f'    Automatic differentiation with compile-time safety')
    print(f'    Type-safe tensor operations')
except Exception as e:
    print(f'    Error loading Spidr results: {e}')
"
    else
        print_error "Spidr results not found"
        exit 1
    fi
}

run_python_experiments() {
    print_step "Running Python linear regression experiments..."

    cd python

    echo " Python implementations with runtime shape checking:"
    echo "   - Manual implementation with explicit verification"
    echo "   - PyTorch implementation with automatic differentiation"
    echo "   - Scikit-learn implementation (optimized solver)"
    echo ""

    python3 linear_python.py \
        --lr ${LEARNING_RATE} \
        --iterations ${MAX_ITERATIONS} \
        --regularization ${REGULARIZATION} || {
        print_error "Python experiments failed"
        exit 1
    }

    cd ..

    print_success "Python experiments completed"
}

run_controlled_comparison() {
    print_step "Running controlled comparison across all implementations..."

    cd python

    echo " Controlled comparison with standardized data:"
    echo "   - All implementations use identical preprocessed dataset"
    echo "   - Same hyperparameters and evaluation metrics"
    echo "   - Fair comparison on identical test set"
    echo "   - Data integrity verified with checksums"
    echo ""

    python3 controlled_comparison.py || {
        print_error "Controlled comparison failed"
        exit 1
    }

    # Save controlled comparison results
    echo " Saving controlled comparison results..."
    python3 -c "
import pandas as pd
import numpy as np
from unified_data_loader import load_standardized_data
from controlled_comparison import load_idris_results, load_spidr_results, run_manual_implementation, run_pytorch_implementation, run_sklearn_implementation, Config

# Load data and run comparison
config = Config()
X_train, X_test, y_train, y_test = load_standardized_data(verbose=False)

# Run all implementations
results = []
results.append(run_manual_implementation(X_train, y_train, X_test, y_test, config))
results.append(run_pytorch_implementation(X_train, y_train, X_test, y_test, config))
results.append(run_sklearn_implementation(X_train, y_train, X_test, y_test, config))

# Load Idris results
idris_result = load_idris_results(X_test, y_test)
if idris_result:
    results.append(idris_result)

# Load Spidr results (if available)
try:
    spidr_result = load_spidr_results(X_test, y_test)
    if spidr_result:
        results.append(spidr_result)
except Exception as e:
    print(f'Could not load Spidr results: {e}')

# Save results summary
with open('../results/controlled_comparison_summary.txt', 'w') as f:
    f.write('CONTROLLED COMPARISON RESULTS\\n')
    f.write('=' * 60 + '\\n')
    f.write(f\"{'Implementation':<12} {'Test MSE':<10} {'Test R²':<8} {'Time(s)':<8} {'Peak MB':<8}\\n\")
    f.write('-' * 60 + '\\n')
    for result in results:
        f.write(f\"{result.name:<12} {result.mse:<10.6f} {result.r2:<8.4f} {result.execution_time:<8.4f} {getattr(result, 'peak_memory_mb', 0.0):<8.2f}\\n\")

    f.write('\\nKey Findings:\\n')
    f.write(' All implementations use identical standardized dataset\\n')
    f.write(' Consistent preprocessing with SHA-256 verification\\n')
    f.write(' Shape safety: Idris (compile-time) vs Python (runtime)\\n')
    f.write(' Fair evaluation on identical test set\\n')

print(' Controlled comparison results saved to results/controlled_comparison_summary.txt')
"

    cd ..

    print_success "Controlled comparison completed"
}

demonstrate_shape_safety() {
    print_step "Demonstrating shape safety benefits..."
    
    cd python
    
    echo " Demonstrating shape mismatches that cause runtime errors in Python:"
    python3 linear_python.py --demo-shapes || {
        print_warning "Shape demonstration completed (errors expected)"
    }
    
    cd ..
    
    echo ""
    echo -e "${GREEN} Idris Prevention:${NC}"
    echo "    All these errors caught at COMPILE TIME"
    echo "    Impossible to create programs with shape mismatches"
    echo "    No runtime shape checking overhead needed"
    echo "    Mathematical guarantees encoded in types"
}

generate_analysis() {
    print_step "Generating comprehensive analysis and visualizations..."
    
    cd python
    
    python3 comparison_plots.py || {
        print_error "Analysis generation failed"
        exit 1
    }
    
    cd ..
    
    print_success "Analysis and visualizations generated"
}

show_results() {
    print_step "Linear Regression Shape Safety Experiment Results"
    echo ""
    
    echo -e "${BLUE} Generated Files:${NC}"
    echo "    Prediction accuracy comparisons"
    echo "    Weight analysis across implementations"
    echo "    Shape safety analysis and verification"
    echo "    Feature importance visualization"
    echo ""
    
    echo -e "${BLUE} Key Findings:${NC}"
    echo ""
    
    echo -e "${GREEN} Compile-Time Shape Safety (Idris):${NC}"
    echo "   - Matrix [m, 8] @ [8, 1] → [m, 1] guaranteed"
    echo "   - Impossible to pass wrong number of features"
    echo "   - No runtime shape checking overhead"
    echo "   - Mathematical constraints encoded in types"
    echo "   - Eliminates entire class of runtime errors"
    echo ""
    
    echo -e "${YELLOW}  Runtime Shape Checking (Python):${NC}"
    echo "   - Shape mismatches detected during execution"
    echo "   - Manual verification required"
    echo "   - ValueError exceptions for dimension mismatches"
    echo "   - Testing needed to catch shape errors"
    echo "   - Performance overhead for runtime checks"
    echo ""
    
    echo -e "${BLUE} Performance Comparison:${NC}"
    if [ -f "results/python_linear_results.txt" ]; then
        echo "   Implementation results:"
        grep -A 5 "Manual Implementation:" results/python_linear_results.txt | sed 's/^/   /'
    fi
    echo ""
    
    echo -e "${BLUE} Result Files:${NC}"
    find results -name "*linear*" -type f | head -10 | sed 's/^/    /'
    if [ $(find results -name "*linear*" -type f | wc -l) -gt 10 ]; then
        echo "   ... and more"
    fi
    echo ""
    
    # California Housing specific results
    echo -e "${BLUE} Standardized California Housing Results:${NC}"
    python3 -c "
import pandas as pd
import numpy as np
import json
try:
    # Load metadata
    with open('data/preprocessing_metadata.json', 'r') as f:
        metadata = json.load(f)

    config = metadata['dataset_config']
    print(f'   - Dataset: {config[\"subset_size\"]} samples ({metadata[\"train_samples\"]} train, {metadata[\"test_samples\"]} test)')
    print(f'   - Features: {config[\"features\"]} (standardized with mean≈0, std≈1)')
    print(f'   - Shape constraint: Exactly 8 features required')
    print(f'   - Task: Continuous price prediction')
    print(f'   - Data consistency: SHA-256 verified')

    # Load predictions if available
    try:
        test_df = pd.read_csv('data/california_test_standardized.csv')
        idris_pred = np.loadtxt('results/idris_linear_predictions.txt')
        y_true = test_df['target'].values

        # Adjust for different sample sizes
        min_len = min(len(idris_pred), len(y_true))
        idris_pred = idris_pred[:min_len]
        y_true = y_true[:min_len]

        mse = np.mean((idris_pred - y_true) ** 2)
        r2 = 1 - np.sum((y_true - idris_pred) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2)
        print(f'   - Idris MSE: {mse:.4f}, R²: {r2:.4f} (on {min_len} samples)')
    except Exception as e:
        print(f'   - Prediction results: Not available ({e})')

except Exception as e:
    print(f'   Error loading results: {e}')
"
    
    print_success "Experiment completed successfully!"
    echo -e "${GREEN} Check the results/ directory for detailed analysis${NC}"
    echo -e "${GREEN} Demonstrates: Compile-time shape safety for ML algorithms${NC}"
}

cleanup() {
    print_step "Cleaning up temporary files..."
    
    # Remove build artifacts
    rm -rf build/
    rm -rf .pack/
    
    print_success "Cleanup completed"
}

test_edge_cases() {
    print_step "Testing edge cases and error scenarios..."
    
    echo " Testing various shape mismatch scenarios..."
    
    cd python
    
    # Test with deliberately wrong feature counts
    echo "Testing feature count mismatches..."
    python3 -c "
import numpy as np
import pandas as pd

# Load correct data
try:
    train_df = pd.read_csv('../data/california_train.csv')
    X_correct = train_df.iloc[:5, :-1].values  # 5 samples, 8 features
    
    print('Testing shape safety edge cases:')
    print(f'Correct input shape: {X_correct.shape}')
    
    # Test various wrong shapes
    test_cases = [
        (7, 'Too few features'),
        (9, 'Too many features'), 
        (5, 'Significantly fewer features'),
        (15, 'Significantly more features')
    ]
    
    for n_features, description in test_cases:
        try:
            X_wrong = np.random.randn(5, n_features)
            W_wrong = np.random.randn(n_features, 1)
            result = X_wrong @ W_wrong
            print(f'     {description}: {X_wrong.shape} @ {W_wrong.shape} = {result.shape} (unexpected success)')
        except ValueError as e:
            print(f'    {description}: Runtime error detected')
            
    print()
    print(' Idris guarantees: ALL these cases caught at compile time!')
    
except Exception as e:
    print(f'Edge case testing failed: {e}')
"
    
    cd ..
    
    print_success "Edge case testing completed"
}

show_help() {
    echo "Linear Regression with Shape Safety Experiment"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -c, --clean         Clean up and rebuild everything"
    echo "  -s, --skip-deps     Skip dependency checking"
    echo "  --lr VALUE          Set learning rate (default: 0.01)"
    echo "  --iterations VALUE  Set max iterations (default: 1000)"
    echo "  --regularization VALUE Set L2 regularization (default: 0.001)"
    echo "  --test-size VALUE   Set test split proportion (default: 0.3)"
    echo "  --demo-shapes       Run shape mismatch demonstrations"
    echo "  --test-edges        Test edge cases and error scenarios"
    echo ""
    echo "Examples:"
    echo "  $0                          # Run full experiment"
    echo "  $0 --lr 0.005 --iterations 2000  # Custom training parameters"
    echo "  $0 --demo-shapes           # Just show shape safety demos"
    echo "  $0 --test-edges            # Include edge case testing"
    echo "  $0 --clean                 # Clean rebuild"
}

# Parse command line arguments
SKIP_DEPS=false
CLEAN=false
DEMO_SHAPES_ONLY=false
TEST_EDGES=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -s|--skip-deps)
            SKIP_DEPS=true
            shift
            ;;
        --lr)
            LEARNING_RATE="$2"
            shift 2
            ;;
        --iterations)
            MAX_ITERATIONS="$2"
            shift 2
            ;;
        --regularization)
            REGULARIZATION="$2"
            shift 2
            ;;
        --test-size)
            TEST_SIZE="$2"
            shift 2
            ;;
        --demo-shapes)
            DEMO_SHAPES_ONLY=true
            shift
            ;;
        --test-edges)
            TEST_EDGES=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_header
    
    echo -e "${BLUE}Configuration:${NC}"
    echo "  Learning rate: ${LEARNING_RATE}"
    echo "  Max iterations: ${MAX_ITERATIONS}"
    echo "  Regularization: ${REGULARIZATION}"
    echo "  Test size: ${TEST_SIZE}"
    echo "  Random seed: ${RANDOM_SEED}"
    echo ""
    
    if [ "$DEMO_SHAPES_ONLY" = true ]; then
        echo " Running shape safety demonstration only..."
        setup_directories
        process_dataset
        demonstrate_shape_safety
        return 0
    fi
    
    if [ "$CLEAN" = true ]; then
        cleanup
        print_warning "Clean mode: will rebuild everything"
    fi
    
    if [ "$SKIP_DEPS" = false ]; then
        check_dependencies
    else
        print_warning "Skipping dependency check"
    fi
    
    setup_directories
    process_dataset
    install_spidr
    build_idris_project
    run_idris_experiment
    run_spidr_experiment
    run_python_experiments
    run_controlled_comparison
    demonstrate_shape_safety

    if [ "$TEST_EDGES" = true ]; then
        test_edge_cases
    fi

    generate_analysis
    show_results
}

# Run main function
main "$@"