# Data Inconsistency Analysis Report

## Executive Summary

The current linear regression comparison experiment suffers from significant data inconsistency issues that compromise the scientific validity of the comparison. This analysis identifies specific problems and provides a roadmap for standardization.

## Current Data Inconsistency Issues

### 1. Multiple Different Datasets in Use

**Problem**: Different implementations use completely different datasets:

- **Idris Implementation**: Uses hardcoded 5-sample synthetic dataset
  ```idris
  sampleInputs : Matrix 5 FEATURES = [
    [1.5, -0.5, 0.8, -0.2, 0.3, -0.1, 0.9, -1.2],
    [0.8, -1.2, -0.3, 0.5, -0.8, 0.4, 0.2, -0.9],
    ...
  ]
  sampleTargets : Vector 5 = [2.5, 1.8, 2.1, 3.2, 1.9]
  ```

- **Python Controlled Comparison**: Uses identical 5-sample dataset (good)
- **Python Full Implementation**: Uses full California Housing dataset (20,640 samples)
  - Training: 14,449 samples
  - Test: 6,193 samples
  - Full: 20,641 samples

**Impact**: Comparing results from 5 samples vs 20,000+ samples is scientifically invalid.

### 2. Inconsistent Data Sources

**Problem**: Multiple data loading mechanisms:

1. **Hardcoded synthetic data** (Idris + controlled comparison)
2. **CSV files from sklearn California Housing** (Python implementations)
3. **No verification** that CSV data matches expected format

**Impact**: No guarantee that "standardized" data is actually consistent.

### 3. Different Train/Test Splits

**Problem**:
- **Idris**: No train/test split (uses same 5 samples for both)
- **Python**: 70/30 split with random_state=42
- **Controlled comparison**: No split (uses same data for train/test)

**Impact**: Cannot fairly compare generalization performance.

### 4. Inconsistent Preprocessing

**Current preprocessing in `data_processing.py`**:
```python
# StandardScaler with fit_transform on train, transform on test
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)
```

**Problems**:
- Idris uses hardcoded "standardized" values (not actually standardized)
- No verification that preprocessing parameters are identical
- Different scaling applied to different dataset sizes

### 5. No Data Integrity Verification

**Missing safeguards**:
- No checksums to verify data consistency
- No validation that all implementations load identical data
- No verification of statistical properties (mean, std, distribution)

### 6. Scale Mismatch Issues

**Current dataset sizes**:
- **Idris**: 5 samples (toy problem)
- **Python full**: 20,640 samples (realistic problem)
- **Different computational complexity**: O(5) vs O(20,000)

**Impact**: Performance comparisons are meaningless due to scale differences.

## Specific Files Affected

### Data Files
- `data/california_train.csv` - 14,449 samples, properly preprocessed
- `data/california_test.csv` - 6,193 samples, properly preprocessed
- `data/california_full.csv` - 20,641 samples, properly preprocessed

### Implementation Files
- `src/LinearRegression.idr` - Uses hardcoded 5-sample data
- `python/controlled_comparison.py` - Uses same 5-sample data (good)
- `python/linear_python.py` - Uses CSV files (inconsistent with Idris)
- `python/data_processing.py` - Generates CSV files not used by Idris

## Scientific Impact

### Current Problems
1. **Invalid comparisons**: Comparing toy (5 samples) vs realistic (20K samples) problems
2. **No reproducibility**: Different random seeds, different data sources
3. **Biased results**: Small dataset favors simple models, large dataset shows different behavior
4. **No verification**: Cannot confirm all implementations use identical data

### Compromised Metrics
- **MSE values**: Not comparable across different dataset sizes
- **R² scores**: Different baseline performance expectations
- **Training time**: Completely different computational complexity
- **Convergence behavior**: Different optimization landscapes

## Recommended Solution Strategy

### Phase 1: Standardized Dataset Creation
1. **Choose California Housing subset**: 1,000 samples (realistic but manageable)
2. **Stratified sampling**: Maintain statistical properties of full dataset
3. **Fixed random seed**: Ensure reproducible subset selection
4. **Proper preprocessing**: Standardized feature scaling with documented parameters

### Phase 2: Data Consistency Verification
1. **SHA-256 checksums**: For all data files (train/test/full)
2. **Statistical validation**: Verify mean≈0, std≈1 for all features
3. **Shape verification**: Ensure all implementations see identical dimensions
4. **Cross-implementation tests**: Verify identical data loading

### Phase 3: Implementation Updates
1. **Idris**: Load from CSV instead of hardcoded data
2. **Python**: Use standardized dataset consistently
3. **Unified preprocessing**: Single source of truth for data transformations
4. **Validation hooks**: All implementations must pass data consistency checks

## Success Criteria

### Data Consistency
- [ ] All implementations use identical 1,000-sample California Housing subset
- [ ] Identical train/test split (700/300 with fixed random_state=42)
- [ ] SHA-256 checksums match across all data files
- [ ] Statistical properties verified (mean≈0, std≈1 for all features)

### Scientific Validity
- [ ] Fair comparison: same dataset size and complexity
- [ ] Reproducible: fixed seeds and deterministic preprocessing
- [ ] Verifiable: automated checks ensure data consistency
- [ ] Realistic: sufficient samples to demonstrate algorithm behavior

### Implementation Consistency
- [ ] All implementations load from same CSV files
- [ ] Identical preprocessing pipeline
- [ ] Same train/test splits
- [ ] Consistent evaluation metrics

## Next Steps

1. **Create standardized subset** (1,000 samples from California Housing)
2. **Implement checksum verification**
3. **Update Idris to load CSV data**
4. **Update Python implementations for consistency**
5. **Add automated validation tests**
6. **Re-run experiments with verified data consistency**

This standardization will ensure the comparison demonstrates the value of compile-time shape safety rather than artifacts from inconsistent data handling.